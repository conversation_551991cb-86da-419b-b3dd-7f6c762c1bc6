[tool:pytest]
# pytest配置文件
# 用于配置测试运行参数和插件设置

# 启用asyncio模式，支持异步测试函数
asyncio_mode = auto

# 测试发现模式
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 添加标记定义，避免未知标记警告
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    asyncio: marks tests as asyncio tests

# 测试输出配置
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes

# 最小版本要求
minversion = 6.0

# 测试路径
testpaths = tests

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:playwright.*
