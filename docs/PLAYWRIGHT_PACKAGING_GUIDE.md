# 指南：打包内置Playwright浏览器的PyInstaller应用
======================================================

本指南详细说明了如何使用PyInstaller打包一个依赖Playwright的Python应用，并将所需的浏览器二进制文件（如Chromium）捆绑到最终分发的包中。这使得最终用户无需自行安装浏览器即可运行程序。

## 核心原理

PyInstaller无法自动发现并打包Playwright下载的浏览器文件。我们的策略是：
1.  **打包应用**: 使用PyInstaller将Python代码打包成一个可执行文件。
2.  **定位浏览器**: 找到Playwright在您的开发机器上缓存浏览器的位置。
3.  **手动复制**: 将整个浏览器缓存目录 (`ms-playwright`) 复制到PyInstaller生成的`dist`目录中，使其与主可执行文件位于同一级别。
4.  **动态路径**: 程序在启动时会自动检测是否为打包环境。如果是，它会动态设置一个环境变量 `PLAYWRIGHT_BROWSERS_PATH`，指向捆绑的浏览器目录。这使得Playwright能够透明地找到并使用这些浏览器。

---

## 步骤一：在开发环境中安装浏览器

在开始打包之前，请确保您已在开发环境中安装了Playwright所需的浏览器。

```bash
# 安装Playwright（如果尚未安装）
pip install playwright

# 安装默认浏览器（主要是Chromium）
playwright install
```

---

## 步骤二：定位Playwright浏览器缓存目录

您需要找到Playwright存放浏览器文件的`ms-playwright`目录。根据操作系统的不同，其默认位置如下：

*   **Windows**: `%USERPROFILE%\AppData\Local\ms-playwright\`
*   **macOS**: `~/Library/Caches/ms-playwright/`
*   **Linux**: `~/.cache/ms-playwright/`

**快速查找技巧**:
您可以运行以下Python命令来精确打印出缓存路径：

```python
from playwright.sync_api import sync_playwright

with sync_playwright() as p:
    # 这个属性指向了Playwright内部工具的路径
    # 其父目录的父目录就是缓存根目录
    print(p._impl_utils.get_driver_dir().parent.parent)
```

请记下这个路径，下一步需要用到。

---

## 步骤三：使用PyInstaller打包应用

我们使用一个`.spec`文件来控制PyInstaller的打包过程，这比直接在命令行中指定所有选项更灵活、更可复用。

1.  **生成`.spec`文件** (如果还没有的话):
    ```bash
    pyi-makespec --name nt-dl-py --onefile --windowed main.py
    ```
    *   `--name`: 设置输出可执行文件的名称。
    *   `--onefile`: 打包成单个可执行文件。
    *   `--windowed`: （可选）如果您的应用是GUI应用，则使用此选项以避免在运行时显示控制台窗口。对于此项目，我们是命令行工具，所以通常不使用此选项。

2.  **编辑`.spec`文件**:
    打开生成的 `nt-dl-py.spec` 文件，并根据需要进行调整。一个典型的配置可能如下所示：

    ```python
    # nt-dl-py.spec
    a = Analysis(['main.py'],
                 pathex=['/path/to/your/project'],
                 binaries=[],
                 datas=[],
                 hiddenimports=[],
                 hookspath=[],
                 runtime_hooks=[],
                 excludes=[],
                 win_no_prefer_redirects=False,
                 win_private_assemblies=False,
                 cipher=None,
                 noarchive=False)
    pyz = PYZ(a.pure, a.zipped_data, cipher=None)
    exe = EXE(pyz,
              a.scripts,
              a.binaries,
              a.zipfiles,
              a.datas,
              [],
              name='nt-dl-py',
              debug=False,
              bootloader_ignore_signals=False,
              strip=False,
              upx=True,
              upx_exclude=[],
              runtime_tmpdir=None,
              console=True) # 对于命令行工具，console=True是必需的
    ```

3.  **执行打包**:
    ```bash
    pyinstaller nt-dl-py.spec
    ```

打包完成后，您会在项目根目录下看到一个 `dist` 文件夹。

---

## 步骤四：将浏览器复制到`dist`目录

这是最关键的一步。

1.  进入 `dist` 目录。您会看到一个与您的应用同名的子目录（例如 `nt-dl-py/`）或直接是可执行文件（取决于`--onedir`或`--onefile`模式）。
2.  将您在**步骤二**中找到的整个 `ms-playwright` 文件夹复制到 `dist/nt-dl-py/` 目录中（如果是`--onedir`模式）或 `dist/` 目录中（如果是`--onefile`模式）。

最终的目录结构应如下所示（以`--onefile`模式为例）：

```
dist/
├── nt-dl-py  (或 nt-dl-py.exe)
└── ms-playwright/
    ├── chromium-1179/
    │   ├── ... (平台特定的浏览器文件)
    │   └── ...
    ├── ffmpeg-1007/
    │   └── ...
    └── ...
```

---

## 最终验证

现在，`dist` 目录已经包含了您的应用和它所需的所有浏览器依赖。您可以将整个`dist`目录压缩并分发给最终用户。当用户运行 `nt-dl-py` 可执行文件时，程序会自动使用捆绑的浏览器，无需任何额外配置。
