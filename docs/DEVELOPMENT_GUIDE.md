# nt-dl-py 开发指南

## 🚀 快速开始

### 环境准备
```bash
# 1. 克隆项目
git clone <repository-url>
cd nt-dl-py

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 3. 安装依赖
pip install -r requirements.txt
pip install playwright
playwright install chromium

# 4. 安装FFmpeg
# macOS: brew install ffmpeg
# Ubuntu: sudo apt install ffmpeg
# Windows: 下载并添加到PATH
```

### 运行测试
```bash
# 基础功能测试
python tests/test_modules.py

# 高级功能测试
python tests/test_advanced_features.py

# 完整测试套件
python tests/check_features.py
```

## 📁 项目架构

### 模块分层
```
应用层 (Application)
├── main.py              # 主程序入口
├── examples/            # 使用示例
└── tests/              # 测试套件

业务层 (Business)
├── src/api.py          # API交互
├── src/downloader.py   # 下载逻辑
└── src/ui.py           # 用户界面

服务层 (Service)
├── src/error_recovery.py    # 错误恢复
├── src/cache_manager.py     # 缓存管理
├── src/network_optimizer.py # 网络优化
└── src/performance.py       # 性能监控

基础层 (Infrastructure)
├── src/config.py       # 配置管理
├── src/utils.py        # 工具函数
├── src/crypto.py       # 加密算法
├── src/env_setup.py    # 环境管理
├── src/logging_utils.py # 日志系统
└── src/file_handler.py  # 文件处理
```

### 核心模块说明

#### 1. 配置管理 (config.py)
- 全局常量定义
- API加密参数
- 下载行为配置
- 文件格式支持

#### 2. 加密算法 (crypto.py)
- weapi双层加密
- AES-128-CBC加密
- RSA公钥加密
- 随机密钥生成

#### 3. API交互 (api.py)
- URL解析路由
- 歌单/专辑数据获取
- 网易云API调用
- 数据格式转换

#### 4. 下载引擎 (downloader.py)
- 浏览器自动化
- 并发下载控制
- 重试机制
- 进度监控

#### 5. 文件处理 (file_handler.py)
- 音频文件解压
- FFmpeg音频处理
- 元数据嵌入
- 临时文件管理

## 🔧 开发规范

### 代码风格
- 使用Python 3.8+特性
- 遵循PEP 8代码规范
- 类型注解覆盖率100%
- 详细的中文注释

### 函数设计原则
```python
def function_name(param: Type) -> ReturnType:
    """
    函数简要描述
    ==============
    
    详细功能说明，包括:
    - 主要用途
    - 处理流程
    - 注意事项
    
    Args:
        param (Type): 参数说明
        
    Returns:
        ReturnType: 返回值说明
        
    Raises:
        ExceptionType: 异常情况说明
        
    Example:
        >>> result = function_name("test")
        >>> assert result == expected
    """
    # 实现代码
    pass
```

### 错误处理
```python
# 使用自定义异常
from src.utils import ProcessingError

try:
    # 可能出错的操作
    result = risky_operation()
except SpecificError as e:
    # 具体错误处理
    raise ProcessingError(f"操作失败: {e}")

# 使用错误恢复装饰器
from src.error_recovery import with_retry, RetryConfig

@with_retry(RetryConfig(max_attempts=3))
async def unstable_function():
    # 可能失败的异步函数
    pass
```

### 日志记录
```python
from src.logging_utils import get_logger

logger = get_logger()

# 不同级别的日志
logger.debug("详细调试信息")
logger.info("一般信息")
logger.warning("警告信息")
logger.error("错误信息")

# 特殊用途日志
logger.log_download_start(url)
logger.log_download_success(url, size, duration)
logger.log_download_failure(url, error)
```

## 🧪 测试指南

### 测试结构
```
tests/
├── test_modules.py          # 基础模块测试
├── test_advanced_features.py # 高级功能测试
├── test_download.py         # 下载功能测试
├── test_enhanced_features.py # 增强功能测试
├── test_main.py            # 主程序测试
├── test_pyinstaller_compat.py # 打包兼容测试
├── check_features.py       # 功能完整性检查
├── verify_packaging_ready.py # 打包准备验证
└── final_test.py           # 最终集成测试
```

### 编写测试
```python
def test_function_name():
    """测试函数功能"""
    # 准备测试数据
    test_input = "test_data"
    expected_output = "expected_result"
    
    # 执行测试
    result = function_to_test(test_input)
    
    # 验证结果
    assert result == expected_output
    print("✅ 测试通过")
    
    return True
```

### 运行特定测试
```bash
# 测试单个模块
python -c "from tests.test_modules import test_crypto_functions; test_crypto_functions()"

# 测试特定功能
python tests/test_advanced_features.py

# 调试模式测试
DEBUG=1 python tests/test_modules.py
```

## 📦 打包部署

### 开发环境打包
```bash
# 生成spec文件
python tools/build_spec.py

# 执行打包
pyinstaller netease_downloader.spec

# 验证打包结果
python tests/verify_packaging_ready.py
```

### 生产环境部署
```bash
# 自动化构建
chmod +x build.sh && ./build.sh  # Linux/macOS
# 或
build.bat                         # Windows

# 手动复制浏览器文件
cp -r ~/.cache/ms-playwright dist/netease_downloader/ms-playwright

# 测试可执行文件
./dist/netease_downloader/netease_downloader
```

## 🔍 调试技巧

### 启用调试模式
```bash
# 设置调试环境变量
export DEBUG=1

# 运行程序查看详细日志
python main.py

# 查看特定模块调试信息
python -c "
import os
os.environ['DEBUG'] = '1'
from src.downloader import *
"
```

### 性能分析
```python
from src.performance import performance_monitor

# 开始监控
stat = performance_monitor.start_download(url)

# 完成监控
performance_monitor.finish_download(stat, success=True, file_size=1024)

# 查看统计
performance_monitor.display_stats()
```

### 缓存调试
```python
from src.cache_manager import cache_manager

# 查看缓存状态
stats = cache_manager.get_stats()
print(f"缓存条目: {stats['memory_entries']}")

# 清理缓存
cache_manager.cleanup_expired()
```

## 🚀 贡献指南

### 提交代码
1. Fork项目仓库
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 编写代码和测试
4. 提交更改: `git commit -m "Add new feature"`
5. 推送分支: `git push origin feature/new-feature`
6. 创建Pull Request

### 代码审查清单
- [ ] 代码符合PEP 8规范
- [ ] 添加了详细的中文注释
- [ ] 包含类型注解
- [ ] 编写了相应测试
- [ ] 测试全部通过
- [ ] 更新了相关文档

### 发布流程
1. 更新版本号
2. 运行完整测试套件
3. 更新CHANGELOG
4. 创建Git标签
5. 构建发布包
6. 发布到仓库

## ⚠️ 开发免责声明

### 🎓 开发学习目的
本开发指南旨在：
- **技术教育**: 教授Python编程和软件工程技术
- **架构学习**: 展示模块化软件架构设计方法
- **最佳实践**: 分享现代软件开发的最佳实践
- **开源贡献**: 促进开源软件的学习和发展

### 🌐 技术来源说明
开发指南中的所有技术内容：
- **公开技术**: 基于公开的编程技术和方法
- **开源实践**: 参考开源项目的开发经验
- **标准协议**: 遵循公开的技术标准和协议
- **教育资源**: 整合公开的编程教育资源

### 🚫 开发责任界限
参与本项目开发的贡献者**不承担**：
- **代码质量**: 不保证代码的绝对正确性和安全性
- **功能完整性**: 不保证所有功能的完整性和可用性
- **兼容性**: 不保证在所有环境下的兼容性
- **维护承诺**: 不承诺长期维护和技术支持

### 📋 开发者义务
参与开发的贡献者应当：
1. **遵守协议**: 遵守开源协议和项目规范
2. **技术合规**: 确保贡献的代码符合技术规范
3. **教育目的**: 明确代码仅用于教育学习目的
4. **责任自担**: 对自己的贡献承担相应责任

### 🔒 代码安全提醒
- **代码审查**: 所有代码都应经过充分的安全审查
- **测试验证**: 确保代码经过充分的测试验证
- **文档完善**: 提供充分的代码文档和说明
- **风险提示**: 明确标注可能的风险和限制

### ⚖️ 贡献责任
- **个人责任**: 每个贡献者对自己的贡献负责
- **集体免责**: 项目整体不承担个人贡献的责任
- **使用风险**: 使用项目代码的风险由使用者承担
- **法律合规**: 贡献者应确保贡献内容合法合规

### 🎯 开发限制
本项目的开发应当：
- ✅ 专注于技术学习和教育价值
- ✅ 保持开源透明的开发方式
- ✅ 遵循软件工程的最佳实践
- ❌ 避免开发可能侵权的功能
- ❌ 避免开发商业化的功能
- ❌ 避免开发可能违法的功能

---

**开发指南版本**: v2.2.0  
**最后更新**: 2024年12月29日  
**维护者**: nt_dl_team  
**开发声明**: 仅供技术学习，开发风险由贡献者自担