# nt-dl-py 部署指南

## 📦 部署概述

nt-dl-py 支持多种部署方式，从开发环境到生产环境，从单机部署到分布式部署，满足不同场景的需求。

## 🚀 快速部署

### 开发环境部署

```bash
# 1. 环境准备
git clone <repository-url>
cd nt-dl-py
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 2. 依赖安装
pip install -r requirements.txt
playwright install chromium

# 3. 外部依赖
# macOS: brew install ffmpeg
# Ubuntu: sudo apt install ffmpeg
# Windows: 下载FFmpeg并添加到PATH

# 4. 验证安装
python tests/test_modules.py
python main.py
```

### 生产环境部署

#### 方式一：可执行文件部署 (推荐)

```bash
# 1. 构建可执行文件
chmod +x build.sh && ./build.sh  # Linux/macOS
# 或 build.bat                   # Windows

# 2. 准备部署包
mkdir nt-dl-py-deploy
cp -r dist/nt-dl-py/* nt-dl-py-deploy/

# 3. 复制浏览器文件 (关键步骤)
# 详细操作请参考: docs/PLAYWRIGHT_PACKAGING_GUIDE.md
cp -r ~/.cache/ms-playwright nt-dl-py-deploy/ms-playwright

# 4. 部署到目标服务器
scp -r nt-dl-py-deploy/ user@server:/opt/nt-dl-py/
```

#### 方式二：Docker容器部署

```dockerfile
# Dockerfile
FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    ffmpeg \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制项目文件
COPY . .

# 安装Python依赖
RUN pip install -r requirements.txt
RUN playwright install chromium
RUN playwright install-deps

# 设置环境变量
ENV PYTHONPATH=/app
ENV DEBUG=0

# 暴露端口（如果需要）
EXPOSE 8080

# 启动命令
CMD ["python", "main.py"]
```

```bash
# 构建和运行Docker容器
docker build -t nt-dl-py:latest .
docker run -it --rm \
  -v /host/downloads:/app/downloads \
  nt-dl-py:latest
```

## 🔧 配置管理

### 环境变量配置

```bash
# 基础配置
export NT_DL_MUSIC_DIR="/data/music"      # 音乐保存目录
export NT_DL_TEMP_DIR="/tmp/nt-dl"        # 临时文件目录
export NT_DL_LOG_LEVEL="INFO"             # 日志级别
export DEBUG="0"                          # 调试模式

# 性能配置
export NT_DL_CONCURRENT_LIMIT="3"         # 并发下载数
export NT_DL_MAX_RETRIES="3"              # 最大重试次数
export NT_DL_DOWNLOAD_TIMEOUT="600"       # 下载超时(秒)

# 网络配置
export NT_DL_PROXY_HTTP=""                # HTTP代理
export NT_DL_PROXY_HTTPS=""               # HTTPS代理
export NT_DL_USER_AGENT="custom-agent"    # 自定义User-Agent
```

### 配置文件管理

```python
# config.local.py (可选的本地配置覆盖)
# 覆盖默认配置
CONCURRENT_LIMIT = 5
MAX_RETRIES = 5
DOWNLOAD_TIMEOUT = 900

# 自定义目录
DEFAULT_MUSIC_DIR = "/custom/music/path"
DEFAULT_TEMP_DIR = "/custom/temp/path"

# 网络配置
PROXY_CONFIG = {
    'http': 'http://proxy.example.com:8080',
    'https': 'https://proxy.example.com:8080'
}
```

## 🏗️ 系统架构部署

### 单机部署架构

```
┌─────────────────────────────────────┐
│           单机部署架构                │
├─────────────────────────────────────┤
│  nt-dl-py 主程序                    │
│  ├── 浏览器自动化 (Playwright)       │
│  ├── 文件处理 (FFmpeg)              │
│  ├── 缓存管理 (本地缓存)             │
│  └── 日志记录 (本地日志)             │
├─────────────────────────────────────┤
│  文件系统                           │
│  ├── /opt/nt-dl-py/ (程序目录)      │
│  ├── /data/music/ (音乐文件)        │
│  ├── /tmp/nt-dl/ (临时文件)         │
│  └── /var/log/nt-dl/ (日志文件)     │
└─────────────────────────────────────┘
```

### 分布式部署架构

```
┌─────────────────┐    ┌─────────────────┐
│   负载均衡器     │    │   监控系统       │
│   (Nginx)       │    │   (Prometheus)  │
└─────────┬───────┘    └─────────────────┘
          │
    ┌─────┴─────┐
    │           │
┌───▼───┐   ┌───▼───┐
│ 节点1  │   │ 节点2  │
│nt-dl-py│   │nt-dl-py│
└───┬───┘   └───┬───┘
    │           │
    └─────┬─────┘
          │
    ┌─────▼─────┐
    │  共享存储  │
    │   (NFS)   │
    └───────────┘
```

## 🔒 安全部署

### 系统安全配置

```bash
# 创建专用用户
sudo useradd -r -s /bin/false nt-dl-py
sudo mkdir -p /opt/nt-dl-py
sudo chown nt-dl-py:nt-dl-py /opt/nt-dl-py

# 设置文件权限
chmod 755 /opt/nt-dl-py/nt-dl-py
chmod 644 /opt/nt-dl-py/*.py
chmod 600 /opt/nt-dl-py/config.local.py

# 配置防火墙 (如果需要)
sudo ufw allow from 192.168.1.0/24 to any port 8080
```

### 容器安全配置

```yaml
# docker-compose.yml
version: '3.8'
services:
  nt-dl-py:
    build: .
    user: "1000:1000"  # 非root用户
    read_only: true     # 只读文件系统
    tmpfs:
      - /tmp
    volumes:
      - ./downloads:/app/downloads:rw
      - ./logs:/app/logs:rw
    environment:
      - DEBUG=0
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETUID
      - SETGID
```

## 📊 监控和日志

### 系统监控

```bash
# 系统资源监控脚本
#!/bin/bash
# monitor.sh

while true; do
    echo "$(date): CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)"
    echo "$(date): Memory: $(free -m | awk 'NR==2{printf "%.1f%%", $3*100/$2}')"
    echo "$(date): Disk: $(df -h /data/music | awk 'NR==2{print $5}')"
    sleep 60
done >> /var/log/nt-dl-py/system.log
```

### 应用监控

```python
# 性能监控集成
from src.performance import performance_monitor

# 在主程序中添加监控
def main():
    # ... 程序逻辑
    
    # 显示性能统计
    stats = performance_monitor.get_session_stats()
    logger.info(f"会话统计: {stats}")
    
    # 发送到监控系统 (可选)
    send_metrics_to_prometheus(stats)
```

### 日志管理

```bash
# logrotate配置
# /etc/logrotate.d/nt-dl-py
/var/log/nt-dl-py/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 nt-dl-py nt-dl-py
    postrotate
        systemctl reload nt-dl-py
    endscript
}
```

## 🔄 自动化部署

### CI/CD流水线

```yaml
# .github/workflows/deploy.yml
name: Deploy nt-dl-py

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pyinstaller
        playwright install chromium
    
    - name: Run tests
      run: |
        python tests/test_modules.py
        python tests/test_advanced_features.py
    
    - name: Build executable
      run: |
        python tools/build_spec.py
        pyinstaller nt-dl-py.spec
    
    - name: Package release
      run: |
        tar -czf nt-dl-py-${{ github.ref_name }}.tar.gz dist/nt-dl-py/
    
    - name: Create Release
      uses: actions/create-release@v1
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ github.ref }}
        draft: false
        prerelease: false
```

### 自动化脚本

```bash
#!/bin/bash
# deploy.sh - 自动化部署脚本

set -e

VERSION=${1:-latest}
DEPLOY_DIR="/opt/nt-dl-py"
BACKUP_DIR="/opt/nt-dl-py-backup"

echo "🚀 开始部署 nt-dl-py $VERSION"

# 1. 备份当前版本
if [ -d "$DEPLOY_DIR" ]; then
    echo "📦 备份当前版本..."
    sudo mv "$DEPLOY_DIR" "$BACKUP_DIR-$(date +%Y%m%d-%H%M%S)"
fi

# 2. 下载新版本
echo "⬇️ 下载版本 $VERSION..."
wget -O nt-dl-py-$VERSION.tar.gz \
    "https://github.com/user/nt-dl-py/releases/download/$VERSION/nt-dl-py-$VERSION.tar.gz"

# 3. 解压和安装
echo "📂 解压和安装..."
sudo mkdir -p "$DEPLOY_DIR"
sudo tar -xzf nt-dl-py-$VERSION.tar.gz -C "$DEPLOY_DIR" --strip-components=1

# 4. 设置权限
echo "🔒 设置权限..."
sudo chown -R nt-dl-py:nt-dl-py "$DEPLOY_DIR"
sudo chmod +x "$DEPLOY_DIR/nt-dl-py"

# 5. 复制浏览器文件
echo "🌐 复制浏览器文件..."
sudo cp -r ~/.cache/ms-playwright "$DEPLOY_DIR/ms-playwright"

# 6. 重启服务
echo "🔄 重启服务..."
sudo systemctl restart nt-dl-py

# 7. 验证部署
echo "✅ 验证部署..."
sleep 5
if sudo systemctl is-active --quiet nt-dl-py; then
    echo "🎉 部署成功！"
else
    echo "❌ 部署失败，回滚到备份版本..."
    sudo systemctl stop nt-dl-py
    sudo mv "$DEPLOY_DIR" "$DEPLOY_DIR-failed"
    sudo mv "$BACKUP_DIR-$(date +%Y%m%d)*" "$DEPLOY_DIR"
    sudo systemctl start nt-dl-py
    exit 1
fi

# 8. 清理
echo "🧹 清理临时文件..."
rm -f nt-dl-py-$VERSION.tar.gz

echo "✨ 部署完成！"
```

## 🔧 运维管理

### 系统服务配置

```ini
# /etc/systemd/system/nt-dl-py.service
[Unit]
Description=nt-dl-py Music Downloader
After=network.target

[Service]
Type=simple
User=nt-dl-py
Group=nt-dl-py
WorkingDirectory=/opt/nt-dl-py
ExecStart=/opt/nt-dl-py/nt-dl-py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# 环境变量
Environment=NT_DL_MUSIC_DIR=/data/music
Environment=NT_DL_LOG_LEVEL=INFO
Environment=DEBUG=0

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/data/music /tmp/nt-dl /var/log/nt-dl-py

[Install]
WantedBy=multi-user.target
```

### 健康检查

```bash
#!/bin/bash
# health_check.sh

# 检查进程
if ! pgrep -f "nt-dl-py" > /dev/null; then
    echo "❌ 进程未运行"
    exit 1
fi

# 检查磁盘空间
DISK_USAGE=$(df /data/music | awk 'NR==2{print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 90 ]; then
    echo "⚠️ 磁盘空间不足: ${DISK_USAGE}%"
    exit 1
fi

# 检查内存使用
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ "$MEMORY_USAGE" -gt 80 ]; then
    echo "⚠️ 内存使用过高: ${MEMORY_USAGE}%"
    exit 1
fi

echo "✅ 系统健康"
exit 0
```

## 📈 性能调优

### 系统级优化

```bash
# 系统参数调优
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 65535' >> /etc/sysctl.conf
echo 'fs.file-max = 100000' >> /etc/sysctl.conf
sysctl -p

# 用户限制调优
echo 'nt-dl-py soft nofile 65535' >> /etc/security/limits.conf
echo 'nt-dl-py hard nofile 65535' >> /etc/security/limits.conf
```

### 应用级优化

```python
# 性能配置建议
PERFORMANCE_CONFIG = {
    # 并发设置 (根据CPU核心数调整)
    'CONCURRENT_LIMIT': min(cpu_count(), 5),
    
    # 网络设置
    'CONNECTION_POOL_SIZE': 20,
    'DNS_CACHE_TTL': 300,
    'KEEPALIVE_TIMEOUT': 30,
    
    # 缓存设置
    'MEMORY_CACHE_SIZE': 1000,
    'DISK_CACHE_TTL': 7200,
    
    # 重试设置
    'MAX_RETRIES': 3,
    'RETRY_BACKOFF_FACTOR': 1.5,
}
```

## ⚠️ 部署免责声明

### 🎯 部署目的限制
本部署指南仅适用于：
- **学习环境**: 用于编程学习和技术研究的环境部署
- **测试环境**: 用于软件测试和功能验证的环境
- **开发环境**: 用于软件开发和调试的环境
- **教育环境**: 用于教学演示和技术培训的环境

### 🚫 禁止的部署场景
严禁将本项目部署用于：
- ❌ **商业生产环境**: 任何形式的商业化服务
- ❌ **大规模服务**: 面向公众的大规模下载服务
- ❌ **盈利性质**: 任何以盈利为目的的服务部署
- ❌ **侵权服务**: 可能侵犯版权的服务部署

### 🌐 第三方服务依赖声明
本项目的部署和运行完全依赖：
- **公益解析站**: 第三方公益性质的音乐解析服务
- **公开API**: 公开可用的网络API接口
- **开源组件**: 开源的第三方软件组件
- **公共服务**: 公共可用的网络服务

### 📋 部署者责任
部署本项目的用户必须：
1. **确保合规**: 确保部署行为符合当地法律法规
2. **控制访问**: 严格控制系统访问权限和使用范围
3. **监控使用**: 监控系统使用情况，防止滥用
4. **承担风险**: 承担部署和运行的所有风险和后果

### 🔒 安全责任声明
- **系统安全**: 部署者负责确保系统安全
- **数据保护**: 部署者负责保护用户数据安全
- **访问控制**: 部署者负责实施适当的访问控制
- **风险防范**: 部署者负责防范各种安全风险

### ⚖️ 法律责任界限
本项目开发者**不承担**：
- **部署后果**: 用户部署本项目产生的任何后果
- **使用行为**: 最终用户的使用行为和法律责任
- **服务可用性**: 部署后服务的可用性和稳定性
- **法律风险**: 部署和使用过程中的法律风险

---

**部署指南版本**: v2.2.0  
**最后更新**: 2024年12月29日  
**支持平台**: Linux | macOS | Windows | Docker  
**重要提醒**: 仅供学习研究环境部署，禁止商业化使用