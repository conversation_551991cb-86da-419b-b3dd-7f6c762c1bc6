#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级使用示例
演示错误恢复、缓存管理、性能监控等高级功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.error_recovery import error_recovery, RetryConfig, with_retry
from src.cache_manager import cache_manager
from src.performance import performance_monitor
from src.network_optimizer import network_optimizer
from src.utils import print_info, print_success, print_error


async def error_recovery_example():
    """错误恢复示例"""
    print_info("🔄 错误恢复示例")
    print_info("=" * 40)
    
    # 模拟一个不稳定的函数
    attempt_count = 0
    
    async def unstable_function():
        nonlocal attempt_count
        attempt_count += 1
        
        if attempt_count < 3:
            raise ConnectionError(f"模拟网络错误 (尝试 {attempt_count})")
        
        return f"成功! (经过 {attempt_count} 次尝试)"
    
    try:
        # 使用错误恢复机制
        config = RetryConfig(
            max_attempts=5,
            base_delay=0.5,
            backoff_factor=1.5
        )
        
        result = await error_recovery.retry_with_recovery(
            unstable_function,
            config=config
        )
        
        print_success(f"✅ {result}")
        
    except Exception as e:
        print_error(f"❌ 最终失败: {e}")


def cache_management_example():
    """缓存管理示例"""
    print_info("\\n💾 缓存管理示例")
    print_info("=" * 40)
    
    # 设置缓存
    song_info = {
        "id": "123456",
        "name": "示例歌曲",
        "artist": "示例艺术家",
        "album": "示例专辑"
    }
    
    cache_key = f"song_info_{song_info['id']}"
    cache_manager.set(cache_key, song_info, ttl=3600)  # 缓存1小时
    print_success("✅ 歌曲信息已缓存")
    
    # 获取缓存
    cached_info = cache_manager.get(cache_key)
    if cached_info:
        print_success(f"✅ 缓存命中: {cached_info['name']}")
    else:
        print_error("❌ 缓存未命中")
    
    # 显示缓存统计
    stats = cache_manager.get_stats()
    print_info(f"缓存统计: {stats['memory_entries']} 个内存条目")
    
    # 清理过期缓存
    expired_count = cache_manager.cleanup_expired()
    print_info(f"清理了 {expired_count} 个过期缓存")


def performance_monitoring_example():
    """性能监控示例"""
    print_info("\\n📊 性能监控示例")
    print_info("=" * 40)
    
    # 模拟下载统计
    urls = [
        "https://example.com/song1.flac",
        "https://example.com/song2.flac",
        "https://example.com/song3.flac"
    ]
    
    for i, url in enumerate(urls):
        # 开始下载监控
        stat = performance_monitor.start_download(url)
        
        # 模拟下载过程
        import time
        time.sleep(0.1)  # 模拟下载时间
        
        # 完成下载
        success = i < 2  # 前两个成功，最后一个失败
        file_size = 5 * 1024 * 1024 if success else None  # 5MB
        error_msg = None if success else "模拟下载失败"
        
        performance_monitor.finish_download(
            stat, 
            success=success, 
            file_size=file_size,
            error_message=error_msg
        )
        
        status = "✅ 成功" if success else "❌ 失败"
        print_info(f"{status}: {url}")
    
    # 显示性能统计
    performance_monitor.display_stats()


async def network_optimization_example():
    """网络优化示例"""
    print_info("\\n⚡ 网络优化示例")
    print_info("=" * 40)
    
    try:
        # 创建优化的HTTP会话
        session = await network_optimizer.create_optimized_session("httpbin.org")
        print_success("✅ 优化会话创建成功")
        
        # 执行优化的请求
        response = await network_optimizer.optimized_request(
            "GET", 
            "https://httpbin.org/json"
        )
        
        if response.status == 200:
            print_success(f"✅ 请求成功: HTTP {response.status}")
        else:
            print_error(f"❌ 请求失败: HTTP {response.status}")
        
        # 显示网络统计
        stats = network_optimizer.get_network_stats()
        print_info(f"网络统计: {stats['total_requests']} 个请求")
        print_info(f"成功率: {stats['success_rate']:.1%}")
        
        # 切换优化模式
        network_optimizer.optimize_for_speed()
        print_info("已切换到速度优化模式")
        
        network_optimizer.optimize_for_stability()
        print_info("已切换到稳定性优化模式")
        
    except Exception as e:
        print_error(f"❌ 网络优化示例失败: {e}")
    
    finally:
        await network_optimizer.cleanup()


@with_retry(RetryConfig(max_attempts=3, base_delay=0.1))
async def decorated_function_example():
    """装饰器函数示例"""
    print_info("\\n🎭 装饰器示例")
    print_info("=" * 40)
    
    # 这个函数使用了重试装饰器
    import random
    
    if random.random() < 0.7:  # 70%概率失败
        raise Exception("随机失败")
    
    return "装饰器重试成功!"


async def comprehensive_workflow_example():
    """综合工作流示例"""
    print_info("\\n🔧 综合工作流示例")
    print_info("=" * 40)
    
    try:
        # 1. 检查缓存
        cache_key = "workflow_data"
        cached_data = cache_manager.get(cache_key)
        
        if cached_data:
            print_success("✅ 使用缓存数据")
            data = cached_data
        else:
            print_info("💾 缓存未命中，获取新数据...")
            
            # 2. 使用错误恢复获取数据
            async def fetch_data():
                # 模拟数据获取
                await asyncio.sleep(0.1)
                return {"timestamp": "2024-12-29", "version": "2.1.0"}
            
            data = await error_recovery.retry_with_recovery(
                fetch_data,
                config=RetryConfig(max_attempts=3)
            )
            
            # 3. 缓存数据
            cache_manager.set(cache_key, data, ttl=1800)  # 缓存30分钟
            print_success("✅ 数据已缓存")
        
        # 4. 处理数据
        print_info(f"处理数据: {data}")
        
        # 5. 性能监控
        stat = performance_monitor.start_download("workflow_example")
        performance_monitor.finish_download(stat, success=True, file_size=1024)
        
        print_success("✅ 综合工作流完成")
        
    except Exception as e:
        print_error(f"❌ 综合工作流失败: {e}")


async def main():
    """主函数"""
    print_info("🚀 网易云音乐下载器 - 高级功能示例")
    print_info("=" * 60)
    
    examples = [
        ("错误恢复", error_recovery_example),
        ("缓存管理", cache_management_example),
        ("性能监控", performance_monitoring_example),
        ("网络优化", network_optimization_example),
        ("装饰器重试", decorated_function_example),
        ("综合工作流", comprehensive_workflow_example),
    ]
    
    for name, example_func in examples:
        try:
            print_info(f"\\n{'='*20} {name} {'='*20}")
            
            if asyncio.iscoroutinefunction(example_func):
                result = await example_func()
                if result:
                    print_success(f"✅ {result}")
            else:
                example_func()
                
        except Exception as e:
            print_error(f"❌ {name} 示例失败: {e}")
    
    print_info("\\n" + "=" * 60)
    print_success("🎉 所有高级功能示例演示完成!")
    
    print_info("\\n🔧 高级功能特性:")
    print_info("  • 智能错误恢复和重试机制")
    print_info("  • 多层缓存管理系统")
    print_info("  • 实时性能监控和统计")
    print_info("  • 网络连接优化")
    print_info("  • 装饰器模式支持")
    print_info("  • 综合工作流集成")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print_info("\\n⚠️ 示例被用户中断")
    except Exception as e:
        print_error(f"\\n❌ 示例运行失败: {e}")