#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本使用示例
演示网易云音乐下载器的基本用法
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.api import parse_and_route_url
from src.downloader import execute_lightweight_download_workflow
from src.env_setup import env_manager
from src.utils import print_info, print_success, print_error


async def basic_download_example():
    """基本下载示例"""
    print_info("🎵 基本下载示例")
    print_info("=" * 40)
    
    # 示例URL（请替换为真实的网易云音乐URL）
    example_urls = [
        "https://music.163.com/song?id=123456",  # 单曲示例
        # "https://music.163.com/playlist?id=123456",  # 歌单示例
        # "https://music.163.com/album?id=123456",  # 专辑示例
    ]
    
    try:
        # 初始化环境
        env_manager.initialize_environment()
        print_success("✅ 环境初始化成功")
        
        for url in example_urls:
            print_info(f"\\n处理URL: {url}")
            
            try:
                # 解析URL
                urls_to_process = parse_and_route_url(url)
                print_success(f"✅ URL解析成功，获得 {len(urls_to_process)} 个歌曲")
                
                # 执行下载（使用高解析度无损音质）
                failed_tasks = await execute_lightweight_download_workflow(
                    urls_to_process,
                    "high_res_lossless",  # 高解析度无损
                    env_manager.temp_dir,
                    env_manager.music_dir,
                    env_manager.current_browser_data_dir
                )
                
                success_count = len(urls_to_process) - len(failed_tasks)
                print_success(f"✅ 下载完成: {success_count}/{len(urls_to_process)} 成功")
                
                if failed_tasks:
                    print_error(f"❌ 失败任务: {len(failed_tasks)} 个")
                
            except Exception as e:
                print_error(f"❌ 处理URL失败: {e}")
    
    except Exception as e:
        print_error(f"❌ 示例执行失败: {e}")
    
    finally:
        # 清理环境
        env_manager.cleanup_and_exit()


async def batch_download_example():
    """批量下载示例"""
    print_info("\\n📦 批量下载示例")
    print_info("=" * 40)
    
    # 多个URL的批量处理
    urls = [
        "https://music.163.com/song?id=123456",
        "https://music.163.com/song?id=123457",
        "https://music.163.com/song?id=123458",
    ]
    
    try:
        env_manager.initialize_environment()
        
        all_urls_to_process = []
        
        # 解析所有URL
        for url in urls:
            try:
                parsed_urls = parse_and_route_url(url)
                all_urls_to_process.extend(parsed_urls)
                print_info(f"✅ 解析URL: {url} -> {len(parsed_urls)} 首歌曲")
            except Exception as e:
                print_error(f"❌ 解析失败: {url} - {e}")
        
        if all_urls_to_process:
            print_info(f"\\n开始批量下载 {len(all_urls_to_process)} 首歌曲...")
            
            # 执行批量下载
            failed_tasks = await execute_lightweight_download_workflow(
                all_urls_to_process,
                "high_res_lossless",
                env_manager.temp_dir,
                env_manager.music_dir,
                env_manager.current_browser_data_dir
            )
            
            success_count = len(all_urls_to_process) - len(failed_tasks)
            print_success(f"\\n🎉 批量下载完成!")
            print_success(f"✅ 成功: {success_count} 首")
            if failed_tasks:
                print_error(f"❌ 失败: {len(failed_tasks)} 首")
        
    except Exception as e:
        print_error(f"❌ 批量下载失败: {e}")
    
    finally:
        env_manager.cleanup_and_exit()


def show_configuration_example():
    """配置示例"""
    print_info("\\n⚙️ 配置示例")
    print_info("=" * 40)
    
    from src.config import CONCURRENT_LIMIT, MAX_RETRIES, DOWNLOAD_TIMEOUT
    
    print_info("当前配置:")
    print_info(f"  并发限制: {CONCURRENT_LIMIT}")
    print_info(f"  最大重试: {MAX_RETRIES}")
    print_info(f"  下载超时: {DOWNLOAD_TIMEOUT}秒")
    
    print_info("\\n自定义配置示例:")
    print_info("```python")
    print_info("# 在src/config.py中修改配置")
    print_info("CONCURRENT_LIMIT = 3  # 增加并发数")
    print_info("MAX_RETRIES = 5       # 增加重试次数")
    print_info("DOWNLOAD_TIMEOUT = 600 # 增加超时时间")
    print_info("```")


def show_environment_example():
    """环境配置示例"""
    print_info("\\n🌍 环境配置示例")
    print_info("=" * 40)
    
    print_info("环境变量配置:")
    print_info("```bash")
    print_info("# 启用调试模式")
    print_info("export DEBUG=1")
    print_info("")
    print_info("# 自定义下载目录")
    print_info("export NT_DL_MUSIC_DIR=/path/to/music")
    print_info("")
    print_info("# 自定义临时目录")
    print_info("export NT_DL_TEMP_DIR=/path/to/temp")
    print_info("```")
    
    print_info("\\n程序化配置:")
    print_info("```python")
    print_info("from src.env_setup import env_manager")
    print_info("")
    print_info("# 设置自定义目录")
    print_info("env_manager.set_music_directory('/custom/music/path')")
    print_info("env_manager.initialize_environment()")
    print_info("```")


async def main():
    """主函数"""
    print_info("🎵 网易云音乐下载器 - 使用示例")
    print_info("=" * 60)
    
    examples = [
        ("基本下载", basic_download_example),
        ("批量下载", batch_download_example),
        ("配置说明", show_configuration_example),
        ("环境配置", show_environment_example),
    ]
    
    for name, example_func in examples:
        try:
            print_info(f"\\n{'='*20} {name} {'='*20}")
            
            if asyncio.iscoroutinefunction(example_func):
                await example_func()
            else:
                example_func()
                
        except Exception as e:
            print_error(f"❌ {name} 示例失败: {e}")
    
    print_info("\\n" + "=" * 60)
    print_success("🎉 所有示例演示完成!")
    print_info("\\n💡 提示:")
    print_info("  1. 请将示例中的URL替换为真实的网易云音乐链接")
    print_info("  2. 确保已安装FFmpeg和Playwright浏览器")
    print_info("  3. 首次使用建议先运行单个URL测试")
    print_info("  4. 查看README.md获取更多详细信息")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print_info("\\n⚠️ 示例被用户中断")
    except Exception as e:
        print_error(f"\\n❌ 示例运行失败: {e}")