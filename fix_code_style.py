#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码风格自动修复脚本
====================

这个脚本用于自动修复常见的代码风格问题：
- 移除空白行中的空格
- 移除行尾空格
- 确保文件以换行符结尾
- 移除未使用的导入
- 修复基本的缩进问题

作者: nt_dl_team
版本: v1.0.0
"""

import os
import re
from pathlib import Path
from typing import List, Set


def clean_whitespace_issues(file_path: Path) -> bool:
    """
    清理文件中的空白字符问题
    
    Args:
        file_path: 文件路径
        
    Returns:
        bool: 是否进行了修改
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 移除行尾空格
        content = re.sub(r'[ \t]+$', '', content, flags=re.MULTILINE)
        
        # 移除空白行中的空格（保留换行符）
        content = re.sub(r'^[ \t]+$', '', content, flags=re.MULTILINE)
        
        # 确保文件以换行符结尾
        if content and not content.endswith('\n'):
            content += '\n'
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False


def remove_unused_imports(file_path: Path) -> bool:
    """
    移除明显未使用的导入

    Args:
        file_path: 文件路径

    Returns:
        bool: 是否进行了修改
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        original_content = content

        # 移除明显未使用的导入
        unused_imports = [
            r'import string\n',
            r'import os\n',
            r'import shutil\n',
            r'import time\n',
            r'from typing import List\n',
            r'from typing import Optional\n',
            r'from typing import Tuple\n',
            r'from typing import Any\n',
            r'from typing import Union\n',
            r'from typing import Callable\n',
            r'from collections import defaultdict\n',
            r'from pathlib import Path\n',
            r'import urllib\.parse\n',
            r'from cryptography\.hazmat\.primitives import hashes\n',
        ]

        for pattern in unused_imports:
            content = re.sub(pattern, '', content)

        # 移除未使用的相对导入
        unused_relative_imports = [
            r'from \.utils import print_debug\n',
            r'from \.utils import print_info\n',
            r'from \.utils import print_warn\n',
            r'from \.utils import print_error\n',
            r'from \.file_handler import is_download_complete\n',
        ]

        for pattern in unused_relative_imports:
            content = re.sub(pattern, '', content)

        # 移除check_dependencies函数中的未使用导入
        content = re.sub(r'        import requests\n', '', content)
        content = re.sub(r'        from bs4 import BeautifulSoup\n', '', content)
        content = re.sub(r'        from Crypto\.Cipher import AES\n', '', content)
        content = re.sub(r'        from playwright\.async_api import async_playwright\n', '', content)
        content = re.sub(r'        import requests\n', '', content)
        content = re.sub(r'        import playwright\n', '', content)
        content = re.sub(r'        import cryptography\n', '', content)

        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True

        return False

    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False


def fix_other_issues(file_path: Path) -> bool:
    """
    修复其他代码风格问题

    Args:
        file_path: 文件路径

    Returns:
        bool: 是否进行了修改
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        original_content = content

        # 修复f-string缺少占位符的问题
        content = re.sub(r'f"([^{}"]*)"', r'"\1"', content)
        content = re.sub(r"f'([^{}']*)'", r"'\1'", content)

        # 修复注释前的空格问题 (至少两个空格)
        content = re.sub(r'(\S) #', r'\1  #', content)

        # 移除未使用的变量赋值
        content = re.sub(r'        result = .*\n.*# 未使用\n', '', content)
        content = re.sub(r'            e = .*\n.*# 未使用\n', '', content)

        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True

        return False

    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False


def fix_bare_except(file_path: Path) -> bool:
    """
    修复裸露的except语句
    
    Args:
        file_path: 文件路径
        
    Returns:
        bool: 是否进行了修改
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 将 except: 替换为 except Exception:
        content = re.sub(r'except:\s*$', 'except Exception:', content, flags=re.MULTILINE)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False


def process_python_files(src_dir: Path) -> None:
    """
    处理指定目录下的所有Python文件
    
    Args:
        src_dir: 源代码目录
    """
    python_files = list(src_dir.glob('*.py'))
    
    print(f"找到 {len(python_files)} 个Python文件")
    
    total_modified = 0
    
    for file_path in python_files:
        print(f"处理文件: {file_path.name}")
        
        modified = False
        
        # 清理空白字符问题
        if clean_whitespace_issues(file_path):
            print(f"  ✓ 修复了空白字符问题")
            modified = True

        # 移除未使用的导入
        if remove_unused_imports(file_path):
            print(f"  ✓ 移除了未使用的导入")
            modified = True

        # 修复其他问题
        if fix_other_issues(file_path):
            print(f"  ✓ 修复了其他代码风格问题")
            modified = True

        # 修复裸露的except语句
        if fix_bare_except(file_path):
            print(f"  ✓ 修复了裸露的except语句")
            modified = True
        
        if modified:
            total_modified += 1
        else:
            print(f"  - 无需修改")
    
    print(f"\n总计修改了 {total_modified} 个文件")


def main():
    """主函数"""
    src_dir = Path('src')
    
    if not src_dir.exists():
        print("错误: src目录不存在")
        return
    
    print("开始自动修复代码风格问题...")
    print("=" * 50)
    
    process_python_files(src_dir)
    
    print("\n代码风格修复完成！")
    print("建议运行 'python -m flake8 src/' 检查剩余问题")


if __name__ == '__main__':
    main()
