#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
附属功能检查脚本
全面检查所有附属功能的完整性和优化空间
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))
import time
import shutil
from pathlib import Path

from src.env_setup import env_manager
from src.file_handler import (
    find_audio_files_recursively, 
    process_failed_downloads,
    handle_temp_directory_cleanup,
    is_download_complete
)
from src.utils import (
    check_ffmpeg, 
    check_dependencies, 
    print_info, 
    print_success, 
    print_warn, 
    print_error
)
from src.config import AUDIO_EXTENSIONS


def check_dependency_management():
    """检查依赖管理功能"""
    print("📦 依赖管理功能检查")
    print("-" * 50)
    
    issues = []
    
    # 检查依赖检查功能
    if check_dependencies():
        print_success("✅ 依赖检查功能正常")
    else:
        print_error("❌ 依赖检查功能异常")
        issues.append("依赖检查功能")
    
    # 检查FFmpeg检查功能
    if check_ffmpeg():
        print_success("✅ FFmpeg检查功能正常")
    else:
        print_warn("⚠️ FFmpeg未安装或不在PATH中")
        issues.append("FFmpeg检查")
    
    # 检查requirements.txt完整性
    req_file = Path("requirements.txt")
    if req_file.exists():
        with open(req_file, 'r') as f:
            content = f.read()
        
        required_packages = ['requests', 'beautifulsoup4', 'pycryptodome', 'playwright']
        missing_packages = []
        
        for package in required_packages:
            if package not in content:
                missing_packages.append(package)
        
        if not missing_packages:
            print_success("✅ requirements.txt 包含所有必需依赖")
        else:
            print_warn(f"⚠️ requirements.txt 缺少依赖: {missing_packages}")
            issues.append("requirements.txt不完整")
    else:
        print_error("❌ requirements.txt 文件不存在")
        issues.append("缺少requirements.txt")
    
    return issues


def check_environment_management():
    """检查环境管理功能"""
    print("\n🏗️ 环境管理功能检查")
    print("-" * 50)
    
    issues = []
    
    try:
        # 初始化环境
        env_manager.initialize_environment()
        
        # 检查目录创建
        directories = [
            ("音乐目录", env_manager.music_dir),
            ("临时目录", env_manager.temp_dir),
            ("浏览器数据目录", env_manager.current_browser_data_dir)
        ]
        
        for name, directory in directories:
            if directory and directory.exists():
                print_success(f"✅ {name}创建正常: {directory}")
            else:
                print_error(f"❌ {name}创建失败")
                issues.append(f"{name}创建")
        
        # 检查目录识别功能
        test_cases = [
            (".browser_data_12345_67890", True),
            ("browser_data_old", True),
            (".browser_data", True),
            ("normal_folder", False),
            ("music_files", False)
        ]
        
        for dir_name, expected in test_cases:
            result = env_manager.is_browser_data_directory(dir_name)
            if result == expected:
                print_success(f"✅ 目录识别正确: {dir_name}")
            else:
                print_error(f"❌ 目录识别错误: {dir_name}")
                issues.append("目录识别功能")
        
        # 检查清理功能 - 使用正确的命名格式
        test_dir = env_manager.music_dir / ".browser_data_123_456"
        test_dir.mkdir(exist_ok=True)
        
        if env_manager.is_browser_data_directory(test_dir.name):
            print_success("✅ 浏览器数据目录识别功能正常")
        else:
            print_error("❌ 浏览器数据目录识别功能异常")
            issues.append("浏览器数据目录识别")
        
        # 清理测试目录
        shutil.rmtree(test_dir, ignore_errors=True)
        
    except Exception as e:
        print_error(f"❌ 环境管理功能测试失败: {e}")
        issues.append("环境管理整体功能")
    
    return issues


def check_file_handling():
    """检查文件处理功能"""
    print("\n📁 文件处理功能检查")
    print("-" * 50)
    
    issues = []
    
    # 检查音频文件扩展名配置
    expected_extensions = {".wav", ".mp3", ".m4a", ".flac", ".aac", ".ogg", ".opus", ".wma", ".aiff", ".alac"}
    if AUDIO_EXTENSIONS == expected_extensions:
        print_success(f"✅ 音频扩展名配置正确 ({len(AUDIO_EXTENSIONS)} 种格式)")
    else:
        print_warn(f"⚠️ 音频扩展名配置可能不完整")
        issues.append("音频扩展名配置")
    
    # 创建测试环境
    test_dir = Path("test_file_handling")
    test_dir.mkdir(exist_ok=True)
    
    try:
        # 创建测试音频文件
        test_files = [
            "test.flac",
            "test.mp3", 
            "test.wav",
            "not_audio.txt",
            "subdir/nested.flac"
        ]
        
        for file_path in test_files:
            full_path = test_dir / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            full_path.write_text("test content")
        
        # 测试音频文件搜索
        found_files = find_audio_files_recursively(test_dir)
        expected_count = 4  # 4个音频文件
        
        if len(found_files) == expected_count:
            print_success(f"✅ 音频文件搜索功能正常 (找到 {len(found_files)} 个文件)")
        else:
            print_error(f"❌ 音频文件搜索功能异常 (期望 {expected_count}，实际 {len(found_files)})")
            issues.append("音频文件搜索")
        
        # 测试下载完成检查
        test_file = test_dir / "download_test.zip"
        test_file.write_bytes(b"test content for download check")
        
        if is_download_complete(test_file):
            print_success("✅ 下载完成检查功能正常")
        else:
            print_warn("⚠️ 下载完成检查功能可能需要优化")
        
    except Exception as e:
        print_error(f"❌ 文件处理功能测试失败: {e}")
        issues.append("文件处理整体功能")
    
    finally:
        # 清理测试目录
        shutil.rmtree(test_dir, ignore_errors=True)
    
    return issues


def check_error_handling():
    """检查错误处理功能"""
    print("\n🛡️ 错误处理功能检查")
    print("-" * 50)
    
    issues = []
    
    # 检查自定义异常
    try:
        from src.utils import ProcessingError
        
        # 测试异常创建和使用
        test_error = ProcessingError("测试错误消息")
        if hasattr(test_error, 'message') and test_error.message == "测试错误消息":
            print_success("✅ 自定义异常类正常")
        else:
            print_error("❌ 自定义异常类异常")
            issues.append("自定义异常类")
    
    except Exception as e:
        print_error(f"❌ 自定义异常测试失败: {e}")
        issues.append("自定义异常")
    
    # 检查错误恢复功能
    try:
        # 模拟临时目录有音频文件的情况
        if not env_manager.temp_dir:
            env_manager.initialize_environment()
        
        test_temp_dir = env_manager.temp_dir / "test_recovery"
        test_temp_dir.mkdir(exist_ok=True)
        
        # 创建测试音频文件
        test_audio = test_temp_dir / "test_recovery.flac"
        test_audio.write_text("test audio content")
        
        # 测试文件恢复功能
        process_failed_downloads(env_manager.temp_dir, env_manager.music_dir)
        
        # 检查是否有恢复的文件
        recovered_files = list(env_manager.music_dir.glob("recovered_*"))
        if recovered_files:
            print_success("✅ 文件恢复功能正常")
            # 清理恢复的文件
            for file in recovered_files:
                file.unlink(missing_ok=True)
        else:
            print_warn("⚠️ 文件恢复功能可能需要优化")
        
        # 清理测试目录
        shutil.rmtree(test_temp_dir, ignore_errors=True)
        
    except Exception as e:
        print_error(f"❌ 错误恢复功能测试失败: {e}")
        issues.append("错误恢复功能")
    
    return issues


def check_user_interface():
    """检查用户界面功能"""
    print("\n🎨 用户界面功能检查")
    print("-" * 50)
    
    issues = []
    
    # 检查颜色输出功能
    try:
        from src.utils import LogColors
        
        required_colors = ['SUCCESS', 'INFO', 'WARN', 'ERROR', 'RESET']
        missing_colors = []
        
        for color in required_colors:
            if not hasattr(LogColors, color):
                missing_colors.append(color)
        
        if not missing_colors:
            print_success("✅ 颜色常量定义完整")
        else:
            print_error(f"❌ 缺少颜色常量: {missing_colors}")
            issues.append("颜色常量")
    
    except Exception as e:
        print_error(f"❌ 颜色输出功能测试失败: {e}")
        issues.append("颜色输出功能")
    
    # 检查UI模块功能
    try:
        from src.ui import extract_song_id_from_url, create_lightweight_task_indicator
        
        # 测试歌曲ID提取
        test_url = "https://music.163.com/song?id=123456"
        song_id = extract_song_id_from_url(test_url)
        
        if song_id == "123456":
            print_success("✅ 歌曲ID提取功能正常")
        else:
            print_error(f"❌ 歌曲ID提取功能异常: 期望 123456，得到 {song_id}")
            issues.append("歌曲ID提取")
        
        # 测试任务指示器
        indicator = create_lightweight_task_indicator(test_url, 1, 5)
        if "123456" in indicator and "1/5" in indicator:
            print_success("✅ 任务指示器功能正常")
        else:
            print_error("❌ 任务指示器功能异常")
            issues.append("任务指示器")
    
    except Exception as e:
        print_error(f"❌ UI模块功能测试失败: {e}")
        issues.append("UI模块功能")
    
    return issues


def check_configuration():
    """检查配置管理功能"""
    print("\n⚙️ 配置管理功能检查")
    print("-" * 50)
    
    issues = []
    
    try:
        from src.config import (
            CONCURRENT_LIMIT, MAX_RETRIES, DOWNLOAD_TIMEOUT,
            TARGET_PAGE_URL, MODULUS, NONCE, PUBKEY, HEADERS
        )
        
        # 检查配置值的合理性
        config_checks = [
            ("CONCURRENT_LIMIT", CONCURRENT_LIMIT, lambda x: isinstance(x, int) and 1 <= x <= 10),
            ("MAX_RETRIES", MAX_RETRIES, lambda x: isinstance(x, int) and 0 <= x <= 5),
            ("DOWNLOAD_TIMEOUT", DOWNLOAD_TIMEOUT, lambda x: isinstance(x, int) and x > 0),
            ("TARGET_PAGE_URL", TARGET_PAGE_URL, lambda x: isinstance(x, str) and x.startswith("http")),
            ("MODULUS", MODULUS, lambda x: isinstance(x, str) and len(x) > 100),
            ("NONCE", NONCE, lambda x: isinstance(x, str) and len(x) == 16),
            ("PUBKEY", PUBKEY, lambda x: isinstance(x, str) and len(x) == 6),
        ]
        
        for name, value, check_func in config_checks:
            if check_func(value):
                print_success(f"✅ {name} 配置正常: {value}")
            else:
                print_error(f"❌ {name} 配置异常: {value}")
                issues.append(f"{name}配置")
        
        # 检查HTTP头配置
        required_headers = ['User-Agent', 'Referer', 'Content-Type']
        missing_headers = []
        
        for header in required_headers:
            if header not in HEADERS:
                missing_headers.append(header)
        
        if not missing_headers:
            print_success("✅ HTTP头配置完整")
        else:
            print_error(f"❌ 缺少HTTP头: {missing_headers}")
            issues.append("HTTP头配置")
    
    except Exception as e:
        print_error(f"❌ 配置管理功能测试失败: {e}")
        issues.append("配置管理功能")
    
    return issues


def check_documentation():
    """检查文档完整性"""
    print("\n📚 文档完整性检查")
    print("-" * 50)
    
    issues = []
    
    # 检查必要文档文件
    required_docs = [
        ("README.md", "使用说明文档"),
        ("requirements.txt", "依赖列表"),
        ("MIGRATION_GUIDE.md", "迁移指南"),
        ("PROJECT_SUMMARY.md", "项目总结")
    ]
    
    for filename, description in required_docs:
        file_path = Path(filename)
        if file_path.exists():
            file_size = file_path.stat().st_size
            if file_size > 100:  # 至少100字节
                print_success(f"✅ {description} 存在且内容充实 ({file_size} 字节)")
            else:
                print_warn(f"⚠️ {description} 存在但内容较少 ({file_size} 字节)")
                issues.append(f"{description}内容不足")
        else:
            print_error(f"❌ {description} 不存在")
            issues.append(f"缺少{description}")
    
    # 检查代码文档
    src_files = list(Path("src").glob("*.py"))
    undocumented_files = []
    
    for src_file in src_files:
        if src_file.name == "__init__.py":
            continue
        
        with open(src_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有模块文档字符串
        if '"""' not in content[:500]:  # 前500字符内应该有文档字符串
            undocumented_files.append(src_file.name)
    
    if not undocumented_files:
        print_success("✅ 所有源码文件都有文档字符串")
    else:
        print_warn(f"⚠️ 以下文件缺少文档字符串: {undocumented_files}")
        issues.append("部分源码文件缺少文档")
    
    return issues


def main():
    """主检查函数"""
    print("🔍 网易云音乐下载器 - 附属功能全面检查")
    print("=" * 70)
    
    # 运行所有检查
    checks = [
        ("依赖管理", check_dependency_management),
        ("环境管理", check_environment_management),
        ("文件处理", check_file_handling),
        ("错误处理", check_error_handling),
        ("用户界面", check_user_interface),
        ("配置管理", check_configuration),
        ("文档完整性", check_documentation),
    ]
    
    all_issues = []
    
    for check_name, check_func in checks:
        try:
            issues = check_func()
            if issues:
                all_issues.extend([(check_name, issue) for issue in issues])
        except Exception as e:
            print_error(f"❌ {check_name} 检查失败: {e}")
            all_issues.append((check_name, f"检查过程异常: {e}"))
    
    # 显示检查结果
    print("\n" + "=" * 70)
    print("📊 附属功能检查结果汇总")
    print("=" * 70)
    
    if not all_issues:
        print_success("🎉 所有附属功能检查通过！系统状态优秀。")
        print("\n💡 建议:")
        print("   - 定期运行此检查脚本确保系统健康")
        print("   - 在添加新功能后重新运行检查")
        print("   - 保持文档和代码同步更新")
    else:
        print_warn(f"⚠️ 发现 {len(all_issues)} 个需要关注的问题:")
        print()
        
        for i, (category, issue) in enumerate(all_issues, 1):
            print(f"{i:2d}. [{category}] {issue}")
        
        print("\n💡 优化建议:")
        
        # 按类别分组问题
        issues_by_category = {}
        for category, issue in all_issues:
            if category not in issues_by_category:
                issues_by_category[category] = []
            issues_by_category[category].append(issue)
        
        for category, issues in issues_by_category.items():
            print(f"\n🔧 {category}优化:")
            for issue in issues:
                print(f"   - 修复: {issue}")
    
    # 清理测试环境
    try:
        env_manager.cleanup_and_exit()
    except:
        pass
    
    return 0 if not all_issues else 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print_warn("\n⚠️ 检查被用户中断")
        sys.exit(0)
    except Exception as e:
        print_error(f"\n❌ 检查过程中发生错误: {e}")
        sys.exit(1)