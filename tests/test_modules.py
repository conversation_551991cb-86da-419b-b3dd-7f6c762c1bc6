#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块测试脚本
验证各个模块的基本功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_imports():
    """测试所有模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from src.config import MODULUS, NONCE, PUBKEY, HEADERS
        print("✅ config 模块导入成功")
    except Exception as e:
        print(f"❌ config 模块导入失败: {e}")
        return False
    
    try:
        from src.utils import print_success, print_info, check_ffmpeg, ProcessingError
        print("✅ utils 模块导入成功")
    except Exception as e:
        print(f"❌ utils 模块导入失败: {e}")
        return False
    
    try:
        from src.crypto import get_weapi_params, generate_random_key
        print("✅ crypto 模块导入成功")
    except Exception as e:
        print(f"❌ crypto 模块导入失败: {e}")
        return False
    
    try:
        from src.api import parse_and_route_url
        print("✅ api 模块导入成功")
    except Exception as e:
        print(f"❌ api 模块导入失败: {e}")
        return False
    
    try:
        from src.env_setup import env_manager
        print("✅ env_setup 模块导入成功")
    except Exception as e:
        print(f"❌ env_setup 模块导入失败: {e}")
        return False
    
    try:
        from src.file_handler import find_audio_files_recursively
        print("✅ file_handler 模块导入成功")
    except Exception as e:
        print(f"❌ file_handler 模块导入失败: {e}")
        return False
    
    try:
        from src.ui import select_audio_quality, display_program_header
        print("✅ ui 模块导入成功")
    except Exception as e:
        print(f"❌ ui 模块导入失败: {e}")
        return False
    
    try:
        from src.downloader import execute_lightweight_download_workflow
        print("✅ downloader 模块导入成功")
    except Exception as e:
        print(f"❌ downloader 模块导入失败: {e}")
        return False
    
    return True

def test_crypto_functions():
    """测试加密功能"""
    print("\n🔐 测试加密功能...")
    
    try:
        from src.crypto import generate_random_key, get_weapi_params
        
        # 测试随机密钥生成
        key = generate_random_key(16)
        if len(key) == 16:
            print("✅ 随机密钥生成正常")
        else:
            print(f"❌ 随机密钥长度错误: {len(key)}")
            return False
        
        # 测试weapi参数生成
        test_payload = {"test": "data", "id": "123456"}
        params = get_weapi_params(test_payload)
        
        if "params" in params and "encSecKey" in params:
            print("✅ weapi参数生成正常")
            print(f"   params长度: {len(params['params'])}")
            print(f"   encSecKey长度: {len(params['encSecKey'])}")
        else:
            print("❌ weapi参数生成失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 加密功能测试失败: {e}")
        return False

def test_environment_setup():
    """测试环境设置"""
    print("\n🏗️ 测试环境设置...")
    
    try:
        from src.env_setup import env_manager
        
        # 模拟初始化（不实际创建目录）
        print("✅ 环境管理器创建成功")
        
        # 测试目录名生成
        test_dir = env_manager.generate_unique_browser_data_dir()
        if test_dir and ".browser_data_" in str(test_dir):
            print("✅ 浏览器数据目录名生成正常")
        else:
            print("❌ 浏览器数据目录名生成失败")
            return False
        
        # 测试目录识别
        test_names = [
            ".browser_data_12345_67890",
            "browser_data_12345",
            "normal_folder",
            ".browser_data"
        ]
        
        for name in test_names:
            is_browser_dir = env_manager.is_browser_data_directory(name)
            expected = name != "normal_folder"
            if is_browser_dir == expected:
                print(f"✅ 目录识别正确: {name} -> {is_browser_dir}")
            else:
                print(f"❌ 目录识别错误: {name} -> {is_browser_dir}, 期望: {expected}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 环境设置测试失败: {e}")
        return False

def test_url_parsing():
    """测试URL解析（不实际发送请求）"""
    print("\n🔗 测试URL解析...")
    
    try:
        from src.api import parse_and_route_url
        
        # 测试单曲URL
        song_url = "https://music.163.com/song?id=123456"
        try:
            # 这会失败，因为我们没有网络请求，但可以测试URL格式识别
            result = parse_and_route_url(song_url)
        except Exception as e:
            # 预期会失败，因为没有实际的网络请求
            if "song?id=123456" in str(e) or "requests" in str(e).lower():
                print("✅ 单曲URL格式识别正常（网络请求失败是预期的）")
            else:
                print(f"❌ 单曲URL解析异常: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ URL解析测试失败: {e}")
        return False

def test_file_operations():
    """测试文件操作"""
    print("\n📁 测试文件操作...")
    
    try:
        from src.file_handler import find_audio_files_recursively
        from src.config import AUDIO_EXTENSIONS
        
        # 测试音频扩展名配置
        if ".flac" in AUDIO_EXTENSIONS and ".mp3" in AUDIO_EXTENSIONS:
            print("✅ 音频扩展名配置正常")
        else:
            print("❌ 音频扩展名配置错误")
            return False
        
        # 测试在当前目录查找音频文件
        current_dir = Path(".")
        audio_files = find_audio_files_recursively(current_dir)
        print(f"✅ 音频文件搜索功能正常，找到 {len(audio_files)} 个音频文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件操作测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始模块化版本测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("加密功能", test_crypto_functions),
        ("环境设置", test_environment_setup),
        ("URL解析", test_url_parsing),
        ("文件操作", test_file_operations),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！模块化版本工作正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关模块。")
        return 1

if __name__ == "__main__":
    sys.exit(main())