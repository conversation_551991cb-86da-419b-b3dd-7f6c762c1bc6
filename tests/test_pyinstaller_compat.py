#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller兼容性测试脚本
测试打包相关功能是否正常工作
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))
from pathlib import Path

# 模拟PyInstaller环境
def simulate_packaged_environment():
    """模拟PyInstaller打包环境"""
    # 临时设置frozen属性
    sys.frozen = True
    
    # 创建模拟的应用程序结构
    test_app_dir = Path("test_packaged_app")
    test_app_dir.mkdir(exist_ok=True)
    
    # 创建模拟的可执行文件
    test_exe = test_app_dir / "netease_downloader"
    test_exe.touch()
    
    # 创建模拟的ms-playwright目录结构
    playwright_dir = test_app_dir / "ms-playwright"
    chromium_dir = playwright_dir / "chromium-1179"
    
    # 根据操作系统创建相应的目录结构
    import platform
    system = platform.system().lower()
    
    if system == "darwin":  # macOS
        chrome_dir = chromium_dir / "chrome-mac" / "Chromium.app" / "Contents" / "MacOS"
        chrome_exe = chrome_dir / "Chromium"
    elif system == "linux":
        chrome_dir = chromium_dir / "chrome-linux"
        chrome_exe = chrome_dir / "chrome"
    elif system == "windows":
        chrome_dir = chromium_dir / "chrome-win"
        chrome_exe = chrome_dir / "chrome.exe"
    else:
        chrome_dir = chromium_dir / "chrome-unknown"
        chrome_exe = chrome_dir / "chrome"
    
    chrome_dir.mkdir(parents=True, exist_ok=True)
    chrome_exe.touch()
    
    # 临时修改sys.executable
    original_executable = sys.executable
    sys.executable = str(test_exe.absolute())
    
    return test_app_dir, original_executable

def restore_environment(test_app_dir, original_executable):
    """恢复原始环境"""
    # 恢复sys.executable
    sys.executable = original_executable
    
    # 删除frozen属性
    if hasattr(sys, 'frozen'):
        delattr(sys, 'frozen')
    
    # 清理测试目录
    import shutil
    shutil.rmtree(test_app_dir, ignore_errors=True)

def test_pyinstaller_detection():
    """测试PyInstaller环境检测"""
    print("🔍 测试PyInstaller环境检测")
    print("-" * 40)
    
    from src.pyinstaller_compat import is_packaged_environment, setup_pyinstaller_environment
    
    # 测试开发环境检测
    if not is_packaged_environment():
        print("✅ 开发环境检测正确")
    else:
        print("❌ 开发环境检测错误")
        return False
    
    # 模拟打包环境
    test_app_dir, original_executable = simulate_packaged_environment()
    
    try:
        # 重新导入模块以获取新的环境状态
        import importlib
        import src.pyinstaller_compat
        importlib.reload(src.pyinstaller_compat)
        
        from src.pyinstaller_compat import is_packaged_environment, setup_pyinstaller_environment
        
        # 测试打包环境检测
        if is_packaged_environment():
            print("✅ 打包环境检测正确")
        else:
            print("❌ 打包环境检测错误")
            return False
        
        # 测试环境设置
        setup_result = setup_pyinstaller_environment()
        if setup_result:
            print("✅ 打包环境设置成功")
        else:
            print("❌ 打包环境设置失败")
            return False
        
        # 检查环境变量是否设置
        if 'PLAYWRIGHT_BROWSERS_PATH' in os.environ:
            browsers_path = os.environ['PLAYWRIGHT_BROWSERS_PATH']
            print(f"✅ 浏览器路径环境变量已设置: {browsers_path}")
        else:
            print("❌ 浏览器路径环境变量未设置")
            return False
        
        return True
        
    finally:
        restore_environment(test_app_dir, original_executable)

def test_browser_path_detection():
    """测试浏览器路径检测"""
    print("\n🔍 测试浏览器路径检测")
    print("-" * 40)
    
    # 模拟打包环境
    test_app_dir, original_executable = simulate_packaged_environment()
    
    try:
        # 重新导入模块
        import importlib
        import src.pyinstaller_compat
        importlib.reload(src.pyinstaller_compat)
        
        from src.pyinstaller_compat import (
            get_browsers_path, 
            get_browser_executable_path,
            verify_browser_installation
        )
        
        # 测试浏览器路径获取
        browsers_path = get_browsers_path()
        if browsers_path and browsers_path.exists():
            print(f"✅ 浏览器路径检测成功: {browsers_path}")
        else:
            print(f"❌ 浏览器路径检测失败: {browsers_path}")
            return False
        
        # 测试可执行文件路径获取
        executable_path = get_browser_executable_path()
        if executable_path and Path(executable_path).exists():
            print(f"✅ 浏览器可执行文件检测成功: {executable_path}")
        else:
            print(f"❌ 浏览器可执行文件检测失败: {executable_path}")
            return False
        
        # 测试浏览器安装验证
        if verify_browser_installation():
            print("✅ 浏览器安装验证成功")
        else:
            print("❌ 浏览器安装验证失败")
            return False
        
        return True
        
    finally:
        restore_environment(test_app_dir, original_executable)

def test_environment_variables():
    """测试环境变量设置"""
    print("\n🔍 测试环境变量设置")
    print("-" * 40)
    
    # 保存原始环境变量
    original_env = os.environ.get('PLAYWRIGHT_BROWSERS_PATH')
    
    try:
        # 模拟打包环境
        test_app_dir, original_executable = simulate_packaged_environment()
        
        try:
            # 重新导入并设置环境
            import importlib
            import src.pyinstaller_compat
            importlib.reload(src.pyinstaller_compat)
            
            from src.pyinstaller_compat import setup_pyinstaller_environment
            
            setup_pyinstaller_environment()
            
            # 检查环境变量
            if 'PLAYWRIGHT_BROWSERS_PATH' in os.environ:
                browsers_path = os.environ['PLAYWRIGHT_BROWSERS_PATH']
                expected_path = str((test_app_dir / "ms-playwright").absolute())
                
                if browsers_path == expected_path:
                    print(f"✅ 环境变量设置正确: {browsers_path}")
                    return True
                else:
                    print(f"❌ 环境变量设置错误: 期望 {expected_path}, 实际 {browsers_path}")
                    return False
            else:
                print("❌ 环境变量未设置")
                return False
                
        finally:
            restore_environment(test_app_dir, original_executable)
            
    finally:
        # 恢复原始环境变量
        if original_env is not None:
            os.environ['PLAYWRIGHT_BROWSERS_PATH'] = original_env
        elif 'PLAYWRIGHT_BROWSERS_PATH' in os.environ:
            del os.environ['PLAYWRIGHT_BROWSERS_PATH']

def test_development_environment():
    """测试开发环境处理"""
    print("\n🔍 测试开发环境处理")
    print("-" * 40)
    
    from src.pyinstaller_compat import (
        is_packaged_environment,
        get_browsers_path,
        get_browser_executable_path
    )
    
    # 确保在开发环境中
    if is_packaged_environment():
        print("❌ 应该在开发环境中，但检测为打包环境")
        return False
    
    # 测试开发环境的路径处理
    browsers_path = get_browsers_path()
    if browsers_path is None:
        print("✅ 开发环境浏览器路径正确返回None")
    else:
        print(f"❌ 开发环境浏览器路径应该返回None，实际返回: {browsers_path}")
        return False
    
    executable_path = get_browser_executable_path()
    if executable_path is None:
        print("✅ 开发环境可执行文件路径正确返回None")
    else:
        print(f"❌ 开发环境可执行文件路径应该返回None，实际返回: {executable_path}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🧪 PyInstaller兼容性测试")
    print("=" * 50)
    
    tests = [
        ("PyInstaller环境检测", test_pyinstaller_detection),
        ("浏览器路径检测", test_browser_path_detection),
        ("环境变量设置", test_environment_variables),
        ("开发环境处理", test_development_environment),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 PyInstaller兼容性测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有PyInstaller兼容性测试通过！")
        print("\n💡 打包准备就绪:")
        print("   ✅ 环境检测功能正常")
        print("   ✅ 浏览器路径处理正常")
        print("   ✅ 环境变量设置正常")
        print("   ✅ 开发/打包环境区分正常")
        print("\n🚀 可以安全进行PyInstaller打包！")
        return 0
    else:
        print("⚠️ 部分PyInstaller兼容性测试失败")
        print("💡 请修复相关问题后再进行打包")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)