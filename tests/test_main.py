#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主程序测试脚本
模拟完整的用户交互流程
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))
from unittest.mock import patch

from src.api import parse_and_route_url
from src.downloader import execute_lightweight_download_workflow
from src.env_setup import env_manager
from src.file_handler import handle_temp_directory_cleanup
from src.ui import display_final_results, display_program_header
from src.utils import check_dependencies, check_ffmpeg, print_error, print_info, print_warn


async def test_main():
    """测试主程序流程"""
    # 检查依赖
    if not check_dependencies():
        return 1
    
    # 初始化环境
    try:
        env_manager.initialize_environment()
    except Exception as e:
        print_error(f"环境初始化失败: {e}")
        return 1
    
    # 显示程序信息
    display_program_header(
        str(env_manager.music_dir),
        str(env_manager.current_browser_data_dir)
    )
    
    # 检查FFmpeg
    if not check_ffmpeg():
        print_error("❌ 严重错误: 未安装FFmpeg或未在系统PATH中找到")
        print_info("💡 在macOS上，您可以通过Homebrew安装: brew install ffmpeg")
        return 1
    
    # 模拟用户输入
    test_url = "https://music.163.com/song?id=22956421"
    print_info(f"🔗 测试URL: {test_url}")
    
    # 解析URL获取歌曲列表
    try:
        urls_to_process = parse_and_route_url(test_url)
    except Exception as e:
        print_error(f"❌ URL解析失败: {e}")
        return 1
    
    if not urls_to_process:
        print_error("❌ 未能获取到有效的URL，程序退出")
        return 1
    
    # 使用默认音质
    selected_quality = "高解析度无损(VIP)"
    print_info(f"🎵 使用音质: {selected_quality}")
    
    # 执行轻量级下载流程
    print_info(f"\n开始处理 {len(urls_to_process)} 个URL")
    failed_tasks = await execute_lightweight_download_workflow(
        urls_to_process,
        selected_quality,
        env_manager.temp_dir,
        env_manager.music_dir,
        env_manager.current_browser_data_dir
    )
    
    # 处理临时文件
    print("\n" + "=" * 25 + " 🧹 清理与恢复 " + "=" * 25)
    handle_temp_directory_cleanup(env_manager.temp_dir, env_manager.music_dir)
    
    # 显示最终结果
    display_final_results(urls_to_process, failed_tasks, str(env_manager.music_dir))
    
    # 程序正常退出清理
    env_manager.cleanup_and_exit()
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(test_main())
        print(f"\n✅ 测试完成，退出码: {exit_code}")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print_warn("\n用户中断了程序。正在退出。")
        env_manager.cleanup_and_exit()
        sys.exit(0)
    except Exception as e:
        print_error(f"程序运行时发生未预期的错误: {e}")
        env_manager.cleanup_and_exit()
        sys.exit(1)