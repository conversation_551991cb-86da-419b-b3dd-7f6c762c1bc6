#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载功能测试脚本
测试单曲下载是否正常工作
"""

import asyncio
import sys
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.env_setup import env_manager
from src.downloader import process_single_url_lightweight
from src.utils import print_info, print_success, print_error

async def test_single_download():
    """测试单曲下载"""
    # 初始化环境
    try:
        env_manager.initialize_environment()
        print_success("环境初始化成功")
    except Exception as e:
        print_error(f"环境初始化失败: {e}")
        return False
    
    # 测试URL（一个简单的单曲）
    test_url = "https://music.163.com/song?id=22956421"
    quality = "高解析度无损(VIP)"
    
    print_info(f"测试URL: {test_url}")
    print_info(f"音质: {quality}")
    
    try:
        await process_single_url_lightweight(
            test_url,
            quality,
            1,
            1,
            env_manager.temp_dir,
            env_manager.music_dir,
            env_manager.current_browser_data_dir
        )
        print_success("✅ 下载测试成功！")
        return True
        
    except Exception as e:
        print_error(f"❌ 下载测试失败: {e}")
        return False
    
    finally:
        # 清理
        env_manager.cleanup_and_exit()

async def main():
    """主测试函数"""
    print("🧪 开始下载功能测试")
    print("=" * 50)
    
    success = await test_single_download()
    
    if success:
        print("\n🎉 测试完成！下载功能正常工作。")
        return 0
    else:
        print("\n❌ 测试失败！请检查错误信息。")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)