#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级功能测试脚本
测试错误恢复、缓存管理等新增功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))
import time
from pathlib import Path

def test_error_recovery():
    """测试错误恢复功能"""
    print("🔄 测试错误恢复功能...")
    
    try:
        from src.error_recovery import error_recovery, RetryConfig, with_retry
        print("✅ 错误恢复模块导入成功")
        
        # 测试重试配置
        config = RetryConfig(max_attempts=3, base_delay=0.1)
        assert config.max_attempts == 3
        assert config.base_delay == 0.1
        print("✅ 重试配置创建正常")
        
        # 测试错误恢复实例
        assert error_recovery is not None
        print("✅ 错误恢复实例创建正常")
        
        return True
    except Exception as e:
        print(f"❌ 错误恢复功能测试失败: {e}")
        return False


def test_cache_manager():
    """测试缓存管理功能"""
    print("💾 测试缓存管理功能...")
    
    try:
        from src.cache_manager import cache_manager, CacheEntry
        print("✅ 缓存管理模块导入成功")
        
        # 测试缓存设置和获取
        test_key = "test_key"
        test_data = {"message": "hello", "timestamp": time.time()}
        
        cache_manager.set(test_key, test_data, ttl=60)
        cached_data = cache_manager.get(test_key)
        
        assert cached_data is not None
        assert cached_data["message"] == "hello"
        print("✅ 缓存设置和获取正常")
        
        # 测试缓存统计
        stats = cache_manager.get_stats()
        assert "memory_entries" in stats
        assert "disk_entries" in stats
        print("✅ 缓存统计功能正常")
        
        # 清理测试缓存
        cache_manager.delete(test_key)
        assert cache_manager.get(test_key) is None
        print("✅ 缓存删除功能正常")
        
        return True
    except Exception as e:
        print(f"❌ 缓存管理功能测试失败: {e}")
        return False


async def test_async_retry():
    """测试异步重试功能"""
    print("🔁 测试异步重试功能...")
    
    try:
        from src.error_recovery import error_recovery, RetryConfig
        
        # 模拟一个会失败的异步函数
        call_count = 0
        
        async def failing_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise ConnectionError(f"模拟连接失败 (尝试 {call_count})")
            return "成功!"
        
        config = RetryConfig(max_attempts=3, base_delay=0.1)
        result = await error_recovery.retry_with_recovery(failing_function, config=config)
        
        assert result == "成功!"
        assert call_count == 3
        print("✅ 异步重试功能正常")
        
        return True
    except Exception as e:
        print(f"❌ 异步重试功能测试失败: {e}")
        return False


def test_performance_enhancements():
    """测试性能增强功能"""
    print("⚡ 测试性能增强功能...")
    
    try:
        from src.performance import performance_monitor, DownloadStats
        print("✅ 性能监控模块导入成功")
        
        # 测试下载统计
        stat = performance_monitor.start_download("test_url")
        assert isinstance(stat, DownloadStats)
        assert stat.url == "test_url"
        print("✅ 下载统计创建正常")
        
        # 模拟完成下载
        performance_monitor.finish_download(stat, success=True, file_size=1024*1024)
        assert stat.success is True
        assert stat.file_size == 1024*1024
        print("✅ 下载统计更新正常")
        
        # 测试会话统计
        session_stats = performance_monitor.get_session_stats()
        assert "total_downloads" in session_stats
        assert session_stats["total_downloads"] >= 1
        print("✅ 会话统计功能正常")
        
        return True
    except Exception as e:
        print(f"❌ 性能增强功能测试失败: {e}")
        return False


def test_logging_enhancements():
    """测试日志增强功能"""
    print("📝 测试日志增强功能...")
    
    try:
        from src.logging_utils import get_logger, ColoredFormatter
        print("✅ 日志模块导入成功")
        
        # 测试日志器创建
        logger = get_logger()
        assert logger is not None
        print("✅ 日志器创建正常")
        
        # 测试日志方法
        logger.info("测试信息日志")
        logger.debug("测试调试日志")
        logger.warning("测试警告日志")
        print("✅ 日志方法调用正常")
        
        # 测试特殊日志方法
        logger.log_download_start("test_url")
        logger.log_download_success("test_url", 1024, 1.5)
        logger.log_download_failure("test_url", "测试错误")
        print("✅ 特殊日志方法正常")
        
        return True
    except Exception as e:
        print(f"❌ 日志增强功能测试失败: {e}")
        return False


def test_config_validation():
    """测试配置验证功能"""
    print("⚙️ 测试配置验证功能...")
    
    try:
        from src.config_validator import validate_config, ConfigValidator
        print("✅ 配置验证模块导入成功")
        
        # 测试配置验证
        is_valid = validate_config()
        assert isinstance(is_valid, bool)
        print("✅ 配置验证功能正常")
        
        # 测试配置验证器
        validator = ConfigValidator()
        assert validator is not None
        print("✅ 配置验证器创建正常")
        
        return True
    except Exception as e:
        print(f"❌ 配置验证功能测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🧪 开始高级功能测试")
    print("=" * 50)
    
    tests = [
        ("错误恢复", test_error_recovery),
        ("缓存管理", test_cache_manager),
        ("异步重试", test_async_retry),
        ("性能增强", test_performance_enhancements),
        ("日志增强", test_logging_enhancements),
        ("配置验证", test_config_validation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 高级功能测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有高级功能测试通过！")
        return 0
    else:
        print("⚠️  部分高级功能测试失败，请检查相关模块。")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)