#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包准备验证脚本
确保所有PyInstaller相关功能都已准备就绪
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))
import subprocess
from pathlib import Path

from src.utils import print_info, print_success, print_error, print_warn


def check_pyinstaller_installation():
    """检查PyInstaller是否已安装"""
    print("📦 检查PyInstaller安装")
    print("-" * 40)
    
    try:
        result = subprocess.run(['pyinstaller', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip()
            print_success(f"✅ PyInstaller已安装: {version}")
            return True
        else:
            print_error("❌ PyInstaller未正确安装")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print_error("❌ PyInstaller未安装")
        print_info("💡 安装命令: pip install pyinstaller")
        return False


def check_dependencies():
    """检查所有依赖是否可用"""
    print("\n📋 检查项目依赖")
    print("-" * 40)
    
    dependencies = [
        ('requests', 'HTTP请求库'),
        ('bs4', 'HTML解析库'),
        ('Crypto', '加密库'),
        ('playwright', '浏览器自动化库'),
    ]
    
    missing_deps = []
    
    for dep, desc in dependencies:
        try:
            __import__(dep)
            print_success(f"✅ {desc}: {dep}")
        except ImportError:
            print_error(f"❌ {desc}: {dep}")
            missing_deps.append(dep)
    
    if missing_deps:
        print_error(f"缺少依赖: {missing_deps}")
        print_info("💡 安装命令: pip install -r requirements.txt")
        return False
    
    return True


def check_project_structure():
    """检查项目结构是否完整"""
    print("\n📁 检查项目结构")
    print("-" * 40)
    
    required_files = [
        ('main.py', '主程序文件'),
        ('requirements.txt', '依赖列表'),
        ('src/__init__.py', '源码包'),
        ('src/pyinstaller_compat.py', 'PyInstaller兼容性模块'),
        ('netease_downloader.spec', 'PyInstaller配置文件'),
    ]
    
    missing_files = []
    
    for file_path, desc in required_files:
        if Path(file_path).exists():
            print_success(f"✅ {desc}: {file_path}")
        else:
            print_error(f"❌ {desc}: {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print_error(f"缺少文件: {missing_files}")
        if 'netease_downloader.spec' in missing_files:
            print_info("💡 生成spec文件: python build_spec.py")
        return False
    
    return True


def check_pyinstaller_compatibility():
    """检查PyInstaller兼容性"""
    print("\n🔧 检查PyInstaller兼容性")
    print("-" * 40)
    
    try:
        # 运行兼容性测试
        result = subprocess.run([sys.executable, 'test_pyinstaller_compat.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print_success("✅ PyInstaller兼容性测试通过")
            return True
        else:
            print_error("❌ PyInstaller兼容性测试失败")
            print_info("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print_error(f"❌ 兼容性测试异常: {e}")
        return False


def check_build_scripts():
    """检查构建脚本"""
    print("\n🛠️ 检查构建脚本")
    print("-" * 40)
    
    build_files = [
        ('build.sh', 'Linux/macOS构建脚本'),
        ('build.bat', 'Windows构建脚本'),
        ('build_spec.py', 'Spec文件生成器'),
    ]
    
    for file_path, desc in build_files:
        if Path(file_path).exists():
            print_success(f"✅ {desc}: {file_path}")
        else:
            print_warn(f"⚠️ {desc}: {file_path} (可选)")
    
    return True


def check_ffmpeg():
    """检查FFmpeg是否可用"""
    print("\n🎵 检查FFmpeg")
    print("-" * 40)
    
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print_success("✅ FFmpeg已安装并可用")
            return True
        else:
            print_error("❌ FFmpeg不可用")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print_error("❌ FFmpeg未安装")
        print_info("💡 macOS安装: brew install ffmpeg")
        return False


def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试基本功能")
    print("-" * 40)
    
    try:
        # 运行模块测试
        result = subprocess.run([sys.executable, 'test_modules.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print_success("✅ 基本功能测试通过")
            return True
        else:
            print_error("❌ 基本功能测试失败")
            return False
            
    except Exception as e:
        print_error(f"❌ 功能测试异常: {e}")
        return False


def generate_packaging_report():
    """生成打包准备报告"""
    print("\n📋 生成打包准备报告")
    print("-" * 40)
    
    report_content = f"""# 打包准备报告

生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 系统信息
- Python版本: {sys.version}
- 操作系统: {__import__('platform').system()} {__import__('platform').release()}
- 架构: {__import__('platform').machine()}

## 检查结果
- PyInstaller: {'✅' if check_pyinstaller_installation() else '❌'}
- 项目依赖: {'✅' if check_dependencies() else '❌'}
- 项目结构: {'✅' if check_project_structure() else '❌'}
- FFmpeg: {'✅' if check_ffmpeg() else '❌'}

## 下一步操作
1. 确保所有检查项都通过
2. 运行构建脚本: ./build.sh (macOS/Linux) 或 build.bat (Windows)
3. 手动复制ms-playwright文件夹到输出目录
4. 测试可执行文件

## 注意事项
- 确保有足够的磁盘空间 (至少2GB)
- 打包过程可能需要几分钟时间
- 首次运行可执行文件时可能需要网络连接
"""
    
    report_file = Path("packaging_report.md")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print_success(f"✅ 报告已生成: {report_file}")
    return report_file


def main():
    """主验证函数"""
    print("🔍 网易云音乐下载器 - 打包准备验证")
    print("=" * 60)
    
    # 运行所有检查
    checks = [
        ("PyInstaller安装", check_pyinstaller_installation),
        ("项目依赖", check_dependencies),
        ("项目结构", check_project_structure),
        ("PyInstaller兼容性", check_pyinstaller_compatibility),
        ("构建脚本", check_build_scripts),
        ("FFmpeg", check_ffmpeg),
        ("基本功能", test_basic_functionality),
    ]
    
    passed = 0
    total = len(checks)
    failed_checks = []
    
    for check_name, check_func in checks:
        try:
            if check_func():
                passed += 1
            else:
                failed_checks.append(check_name)
        except Exception as e:
            print_error(f"❌ {check_name} 检查异常: {e}")
            failed_checks.append(check_name)
    
    # 生成报告
    generate_packaging_report()
    
    # 显示结果
    print("\n" + "=" * 60)
    print(f"📊 打包准备验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print_success("🎉 所有检查通过！可以开始打包。")
        print("\n🚀 下一步操作:")
        print("   1. 运行构建脚本:")
        print("      macOS/Linux: ./build.sh")
        print("      Windows: build.bat")
        print("   2. 手动复制ms-playwright文件夹")
        print("   3. 测试可执行文件")
        print("\n📚 详细说明请查看: PACKAGING_GUIDE.md")
        return 0
    else:
        print_error("⚠️ 部分检查失败，请修复后再进行打包")
        print(f"\n❌ 失败的检查: {', '.join(failed_checks)}")
        print("\n💡 修复建议:")
        if "PyInstaller安装" in failed_checks:
            print("   - 安装PyInstaller: pip install pyinstaller")
        if "项目依赖" in failed_checks:
            print("   - 安装依赖: pip install -r requirements.txt")
        if "项目结构" in failed_checks:
            print("   - 生成spec文件: python build_spec.py")
        if "FFmpeg" in failed_checks:
            print("   - 安装FFmpeg: brew install ffmpeg (macOS)")
        return 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print_warn("\n⚠️ 验证被用户中断")
        sys.exit(0)
    except Exception as e:
        print_error(f"\n❌ 验证过程中发生错误: {e}")
        sys.exit(1)