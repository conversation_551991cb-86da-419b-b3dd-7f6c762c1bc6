#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终完整功能测试
测试单曲、歌单、专辑的解析功能
"""

import asyncio
import sys
from pathlib import Path

import pytest

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))
from pathlib import Path

from src.api import parse_and_route_url
from src.env_setup import env_manager
from src.utils import print_info, print_success, print_error, print_warn


@pytest.mark.asyncio
async def test_url_parsing():
    """测试URL解析功能"""
    print("🔗 URL解析功能测试")
    print("=" * 50)

    # 测试有效的单曲URL
    test_url = "https://music.163.com/song?id=22956421"
    print(f"\n📋 测试单曲URL: {test_url}")

    try:
        urls = parse_and_route_url(test_url)
        # 对于单曲，应该返回包含一个URL的列表
        assert isinstance(urls, list), "解析结果应该是列表"
        assert len(urls) >= 1, "单曲解析应该至少返回一个URL"
        print_success(f"✅ 单曲解析成功，获得 {len(urls)} 个歌曲URL")
    except Exception as e:
        # 网络问题或API变化可能导致解析失败，这在测试中是可接受的
        if "URL格式不匹配" in str(e):
            pytest.fail(f"URL格式验证失败: {e}")
        else:
            print_warn(f"⚠️ 单曲解析失败（可能是网络问题）: {e}")

    # 测试无效URL应该抛出异常
    invalid_url = "https://invalid-url.com/test"
    print(f"\n📋 测试无效URL: {invalid_url}")

    with pytest.raises(Exception):
        parse_and_route_url(invalid_url)
    print_success("✅ 无效URL正确抛出异常")


@pytest.mark.asyncio
async def test_environment_management():
    """测试环境管理功能"""
    print("\n🏗️ 环境管理功能测试")
    print("=" * 50)
    
    try:
        # 初始化环境
        env_manager.initialize_environment()
        print_success("✅ 环境初始化成功")
        
        # 检查目录创建
        assert env_manager.music_dir.exists(), "音乐目录创建失败"
        print_success(f"✅ 音乐目录创建成功: {env_manager.music_dir}")

        assert env_manager.temp_dir.exists(), "临时目录创建失败"
        print_success(f"✅ 临时目录创建成功: {env_manager.temp_dir}")

        assert env_manager.current_browser_data_dir.exists(), "浏览器数据目录创建失败"
        print_success(f"✅ 浏览器数据目录创建成功: {env_manager.current_browser_data_dir}")
        
        # 测试目录识别
        test_dirs = [
            ".browser_data_12345_67890",
            "normal_folder",
            ".browser_data"
        ]
        
        for dir_name in test_dirs:
            is_browser = env_manager.is_browser_data_directory(dir_name)
            expected = dir_name != "normal_folder"
            assert is_browser == expected, f"目录识别错误: {dir_name}"
            print_success(f"✅ 目录识别正确: {dir_name}")

    except Exception as e:
        print_error(f"❌ 环境管理测试失败: {e}")
        pytest.fail(f"环境管理测试失败: {e}")


@pytest.mark.asyncio
async def test_crypto_functionality():
    """测试加密功能"""
    print("\n🔐 加密功能测试")
    print("=" * 50)

    from src.crypto import generate_random_key, get_weapi_params

    # 测试随机密钥生成
    key1 = generate_random_key(16)
    key2 = generate_random_key(16)

    assert len(key1) == 16, "密钥长度应为16"
    assert len(key2) == 16, "密钥长度应为16"
    assert key1 != key2, "两次生成的密钥应该不同"
    print_success("✅ 随机密钥生成正常")

    # 测试weapi加密
    test_payload = {"test": "data", "id": "123456"}
    params1 = get_weapi_params(test_payload)
    params2 = get_weapi_params(test_payload)

    assert "params" in params1, "params字段缺失"
    assert "encSecKey" in params1, "encSecKey字段缺失"
    assert "params" in params2, "params字段缺失"
    assert "encSecKey" in params2, "encSecKey字段缺失"
    assert params1["params"] != params2["params"], "两次加密结果应该不同（因为随机密钥）"

    print_success("✅ weapi加密功能正常")
    print_info(f"   params长度: {len(params1['params'])}")
    print_info(f"   encSecKey长度: {len(params1['encSecKey'])}")


async def main():
    """主测试函数"""
    print("🧪 网易云音乐下载器 - 最终完整功能测试")
    print("=" * 70)
    
    # 运行所有测试
    tests = [
        ("URL解析功能", test_url_parsing),
        ("环境管理功能", test_environment_management),
        ("加密功能", test_crypto_functionality),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            if isinstance(result, list):
                # URL解析测试返回详细结果
                success = all(r[1] for r in result)
                results.append((test_name, success, result))
            else:
                results.append((test_name, result, None))
        except Exception as e:
            print_error(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False, None))
    
    # 显示测试结果
    print("\n" + "=" * 70)
    print("📊 测试结果汇总")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, success, details in results:
        if success:
            print_success(f"✅ {test_name}: 通过")
            passed += 1
            if details and isinstance(details, list):
                for detail_type, detail_success, count in details:
                    if detail_success and count > 0:
                        print_info(f"   - {detail_type}: {count} 个URL")
        else:
            print_error(f"❌ {test_name}: 失败")
    
    print("\n" + "=" * 70)
    print(f"📈 总体结果: {passed}/{total} 测试通过")
    
    # 清理测试环境
    try:
        env_manager.cleanup_and_exit()
    except:
        pass
    
    if passed == total:
        print_success("🎉 所有测试通过！模块化版本功能完全正常。")
        print("\n💡 可以安全使用以下命令运行完整程序:")
        print("   python main.py")
        return 0
    else:
        print_error("⚠️ 部分测试失败，请检查相关功能。")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print_warn("\n⚠️ 测试被用户中断")
        sys.exit(0)
    except Exception as e:
        print_error(f"\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)