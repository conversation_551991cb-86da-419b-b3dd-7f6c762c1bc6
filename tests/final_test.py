#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终完整功能测试
测试单曲、歌单、专辑的解析功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))
from pathlib import Path

from src.api import parse_and_route_url
from src.env_setup import env_manager
from src.utils import print_info, print_success, print_error, print_warn


async def test_url_parsing():
    """测试URL解析功能"""
    print("🔗 URL解析功能测试")
    print("=" * 50)
    
    test_cases = [
        ("单曲", "https://music.163.com/song?id=22956421"),
        ("歌单", "https://music.163.com/playlist?id=789012"),
        ("专辑", "https://music.163.com/album?id=345678"),
        ("无效URL", "https://invalid-url.com/test"),
    ]
    
    results = []
    
    for test_type, url in test_cases:
        print(f"\n📋 测试 {test_type}: {url}")
        try:
            urls = parse_and_route_url(url)
            if urls:
                print_success(f"✅ {test_type} 解析成功，获得 {len(urls)} 个歌曲URL")
                results.append((test_type, True, len(urls)))
            else:
                print_warn(f"⚠️ {test_type} 解析成功但未获得歌曲URL")
                results.append((test_type, True, 0))
        except Exception as e:
            if "URL格式不匹配" in str(e):
                print_error(f"❌ {test_type} URL格式不匹配（预期结果）")
                results.append((test_type, True, 0))  # 对于无效URL，这是预期结果
            else:
                print_error(f"❌ {test_type} 解析失败: {e}")
                results.append((test_type, False, 0))
    
    return results


async def test_environment_management():
    """测试环境管理功能"""
    print("\n🏗️ 环境管理功能测试")
    print("=" * 50)
    
    try:
        # 初始化环境
        env_manager.initialize_environment()
        print_success("✅ 环境初始化成功")
        
        # 检查目录创建
        if env_manager.music_dir.exists():
            print_success(f"✅ 音乐目录创建成功: {env_manager.music_dir}")
        else:
            print_error("❌ 音乐目录创建失败")
            return False
        
        if env_manager.temp_dir.exists():
            print_success(f"✅ 临时目录创建成功: {env_manager.temp_dir}")
        else:
            print_error("❌ 临时目录创建失败")
            return False
        
        if env_manager.current_browser_data_dir.exists():
            print_success(f"✅ 浏览器数据目录创建成功: {env_manager.current_browser_data_dir}")
        else:
            print_error("❌ 浏览器数据目录创建失败")
            return False
        
        # 测试目录识别
        test_dirs = [
            ".browser_data_12345_67890",
            "normal_folder",
            ".browser_data"
        ]
        
        for dir_name in test_dirs:
            is_browser = env_manager.is_browser_data_directory(dir_name)
            expected = dir_name != "normal_folder"
            if is_browser == expected:
                print_success(f"✅ 目录识别正确: {dir_name}")
            else:
                print_error(f"❌ 目录识别错误: {dir_name}")
                return False
        
        return True
        
    except Exception as e:
        print_error(f"❌ 环境管理测试失败: {e}")
        return False


async def test_crypto_functionality():
    """测试加密功能"""
    print("\n🔐 加密功能测试")
    print("=" * 50)
    
    try:
        from src.crypto import generate_random_key, get_weapi_params
        
        # 测试随机密钥生成
        key1 = generate_random_key(16)
        key2 = generate_random_key(16)
        
        if len(key1) == 16 and len(key2) == 16 and key1 != key2:
            print_success("✅ 随机密钥生成正常")
        else:
            print_error("❌ 随机密钥生成异常")
            return False
        
        # 测试weapi加密
        test_payload = {"test": "data", "id": "123456"}
        params1 = get_weapi_params(test_payload)
        params2 = get_weapi_params(test_payload)
        
        if ("params" in params1 and "encSecKey" in params1 and
            "params" in params2 and "encSecKey" in params2 and
            params1["params"] != params2["params"]):  # 应该不同，因为随机密钥
            print_success("✅ weapi加密功能正常")
            print_info(f"   params长度: {len(params1['params'])}")
            print_info(f"   encSecKey长度: {len(params1['encSecKey'])}")
        else:
            print_error("❌ weapi加密功能异常")
            return False
        
        return True
        
    except Exception as e:
        print_error(f"❌ 加密功能测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🧪 网易云音乐下载器 - 最终完整功能测试")
    print("=" * 70)
    
    # 运行所有测试
    tests = [
        ("URL解析功能", test_url_parsing),
        ("环境管理功能", test_environment_management),
        ("加密功能", test_crypto_functionality),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            if isinstance(result, list):
                # URL解析测试返回详细结果
                success = all(r[1] for r in result)
                results.append((test_name, success, result))
            else:
                results.append((test_name, result, None))
        except Exception as e:
            print_error(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False, None))
    
    # 显示测试结果
    print("\n" + "=" * 70)
    print("📊 测试结果汇总")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, success, details in results:
        if success:
            print_success(f"✅ {test_name}: 通过")
            passed += 1
            if details and isinstance(details, list):
                for detail_type, detail_success, count in details:
                    if detail_success and count > 0:
                        print_info(f"   - {detail_type}: {count} 个URL")
        else:
            print_error(f"❌ {test_name}: 失败")
    
    print("\n" + "=" * 70)
    print(f"📈 总体结果: {passed}/{total} 测试通过")
    
    # 清理测试环境
    try:
        env_manager.cleanup_and_exit()
    except:
        pass
    
    if passed == total:
        print_success("🎉 所有测试通过！模块化版本功能完全正常。")
        print("\n💡 可以安全使用以下命令运行完整程序:")
        print("   python main.py")
        return 0
    else:
        print_error("⚠️ 部分测试失败，请检查相关功能。")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print_warn("\n⚠️ 测试被用户中断")
        sys.exit(0)
    except Exception as e:
        print_error(f"\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)