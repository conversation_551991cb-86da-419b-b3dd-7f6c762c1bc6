#!/bin/bash
# 网易云音乐下载器自动化构建脚本

set -e  # 遇到错误立即退出

echo "🚀 开始构建nt-dl-py..."

# 检查Python环境
echo "📋 检查Python环境..."
python --version
pip --version

# 安装依赖
echo "📦 安装依赖包..."
pip install -r requirements.txt
pip install pyinstaller

# 运行测试
echo "🧪 运行测试..."
python tests/test_modules.py

# 创建spec文件
echo "📝 创建spec文件..."
python tools/build_spec.py

# 执行打包
echo "📦 开始打包..."
pyinstaller netease_downloader.spec

# 检查打包结果
if [ -d "dist/netease_downloader" ]; then
    echo "✅ 打包成功！"
    echo "📁 输出目录: dist/netease_downloader/"
    echo ""
    echo "⚠️  重要提醒:"
    echo "请手动将ms-playwright文件夹复制到 dist/netease_downloader/ 目录下"
    echo "文件夹结构应为:"
    echo "  dist/netease_downloader/"
    echo "  ├── netease_downloader (可执行文件)"
    echo "  ├── ms-playwright/"
    echo "  │   └── chromium-1179/"
    echo "  │       └── ..."
    echo "  └── ..."
    echo ""
    echo "🎉 构建完成！"
else
    echo "❌ 打包失败！"
    exit 1
fi
