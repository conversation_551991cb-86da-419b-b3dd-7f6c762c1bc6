# -*- coding: utf-8 -*-
"""
全局配置模块 - 系统常量和参数定义
=====================================

这个模块包含了网易云音乐下载器的所有全局配置常量和参数。
所有的配置项都在这里集中管理，便于维护和调整。

配置分类:
- 下载行为配置: 并发数、重试次数、超时时间等
- 网易云API配置: 加密参数、请求头、目标URL等  
- 文件处理配置: 支持的音频格式、文件扩展名等

注意事项:
- 修改这些配置可能会影响程序的稳定性和性能
- 加密相关的常量不应随意修改，它们与网易云API协议相关
- 并发限制和超时时间应根据网络环境和系统性能调整

作者: nt_dl_team
版本: v2.2.0

⚠️ 配置声明:
本配置文件中的所有参数和常量仅用于技术学习和研究。
- API参数: 基于公开的网络协议规范
- 加密常量: 来源于公开的技术文档
- 网络配置: 用于技术实现和协议分析
- 使用限制: 仅供学习研究，禁止用于侵权行为
- 免责声明: 配置使用风险由用户自行承担
"""

# ==================== 下载行为配置 ====================

# 并发下载任务数限制
# 控制同时进行的下载任务数量，避免对服务器造成过大压力
# 建议值: 1-5，过高可能导致请求被限制或系统资源不足
CONCURRENT_LIMIT = 2

# 最大重试次数
# 当下载失败时的重试次数，包括网络错误、超时等情况
# 建议值: 2-5，过高可能导致程序长时间卡住
MAX_RETRIES = 2

# 下载超时时间（秒）
# 单个下载任务的最大等待时间，超过此时间将被视为失败
# 480秒 = 8分钟，适合大部分网络环境和文件大小
DOWNLOAD_TIMEOUT = 480

# 目标解析网站URL
# 用于解析网易云音乐链接的第三方API服务
# 这个服务提供了网易云音乐的解析接口
TARGET_PAGE_URL = "https://api.toubiec.cn/wyapi.html"

# ==================== 网易云API加密配置 ====================

# RSA加密模数 (Modulus)
# 这是网易云音乐API使用的RSA公钥的模数部分
# 用于weapi接口的双层加密算法（AES + RSA）
# 注意: 这个值是从网易云官方获取的，不应随意修改
MODULUS = (
    "00e0b509f6259df8642dbc35662901477df22677ec152b5ff68ace615bb7b725152b3ab17a876aea8a5aa76d2e417629ec4ee341f56135fccf695280104e0312ecbda92557c93870114af6c9d05c4f7f0c3685b7a46bee255932575cce10b424d813cfe4875d3e82047b97ddef52741d546b8e289dc6935b3ece0462db0a22b8e7"
)

# AES加密的固定nonce值
# 用于AES加密的初始化向量，这是网易云API的固定值
NONCE = "0CoJUm6Qyw8W8jud"

# RSA公钥指数
# RSA加密算法的公钥指数部分，通常为65537（十六进制0x010001）
PUBKEY = "010001"

# ==================== HTTP请求配置 ====================

# HTTP请求头配置
# 模拟真实浏览器的请求头，避免被服务器识别为爬虫
HEADERS = {
    # 用户代理字符串，模拟Chrome浏览器
    # 使用较新的Chrome版本号以获得更好的兼容性
    'User-Agent': (
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 '
        '(KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36'
    ),
    
    # 引用页面，表明请求来源于网易云音乐官网
    'Referer': 'https://music.163.com/',
    
    # 内容类型，用于POST请求的表单数据
    'Content-Type': 'application/x-www-form-urlencoded',
    
    # 接受的语言，优先中文
    'Accept-Language': 'zh-CN,zh;q=0.9',
}

# ==================== 文件格式配置 ====================

# 支持的音频文件扩展名集合
# 用于识别和处理各种音频格式文件
# 包含了常见的无损和有损音频格式
AUDIO_EXTENSIONS = {
    ".wav",   # 无损音频格式，未压缩
    ".mp3",   # 有损压缩格式，最常见
    ".m4a",   # Apple的音频格式，通常为AAC编码
    ".flac",  # 无损压缩格式，高音质
    ".aac",   # 高效音频编码，有损压缩
    ".ogg",   # 开源音频格式，通常为Vorbis编码
    ".opus",  # 现代音频编码，低延迟高质量
    ".wma",   # Windows Media Audio格式
    ".aiff",  # Apple的无损音频格式
    ".alac"   # Apple无损音频编解码器
}