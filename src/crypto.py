# -*- coding: utf-8 -*-
"""
加密模块 - 网易云音乐API加密算法实现
=====================================

这个模块实现了网易云音乐weapi接口所需的完整加密算法。
网易云音乐使用双层加密机制来保护API请求数据：
1. 第一层：使用固定nonce进行AES加密
2. 第二层：使用随机密钥进行AES加密  
3. 第三层：使用RSA公钥加密随机密钥

加密流程:
原始数据 → JSON序列化 → AES(nonce) → AES(random_key) → 最终params
random_key → RSA加密 → encSecKey

这个双层加密机制确保了请求数据的安全性，防止被轻易破解。

技术细节:
- AES加密: CBC模式，PKCS7填充
- RSA加密: 使用网易云提供的公钥
- 随机密钥: 16位字母数字组合
- 字符串反转: RSA加密前需要反转字节序

作者: nt_dl_team
版本: v2.2.0
基于: 网易云音乐官方API加密协议
"""

import json
import random
import string
from base64 import b64encode
from typing import Dict

from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from Crypto.Util.Padding import pad

from .config import MODULUS, NONCE, PUBKEY


def generate_random_key(length: int) -> str:
    """
    生成指定长度的随机密钥
    ========================
    
    为AES加密生成随机密钥，使用字母和数字的组合。
    每次请求都会生成不同的密钥，增强安全性。
    
    Args:
        length (int): 密钥长度，通常为16位
        
    Returns:
        str: 生成的随机密钥字符串
        
    Example:
        >>> key = generate_random_key(16)
        >>> len(key)
        16
        >>> key  # 类似 "aB3dE5fG7hI9jK1L"
    
    Note:
        密钥字符集包含大小写字母和数字，共62个字符，
        16位密钥的组合数约为 62^16 ≈ 4.7 × 10^28
    """
    # 定义密钥字符集：大小写字母 + 数字
    seed = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    # 随机选择指定长度的字符组合
    return "".join(random.choices(seed, k=length))


def aes_encrypt(data: str, key: str) -> str:
    """
    AES加密函数
    =============
    
    使用AES-128-CBC模式对数据进行加密，这是网易云API的标准加密方式。
    
    加密参数:
    - 算法: AES-128 (密钥长度128位)
    - 模式: CBC (密码块链接模式)
    - 填充: PKCS7填充
    - IV: 固定初始化向量 "0102030405060708"
    
    Args:
        data (str): 要加密的原始数据
        key (str): AES加密密钥
        
    Returns:
        str: Base64编码的加密结果
        
    Example:
        >>> encrypted = aes_encrypt("hello", "mykey1234567890")
        >>> isinstance(encrypted, str)
        True
    
    Technical Details:
        1. 将数据和密钥转换为UTF-8字节
        2. 使用PKCS7填充对齐到16字节块
        3. 执行AES-CBC加密
        4. 将结果进行Base64编码
    """
    # 固定的初始化向量，网易云API的标准配置
    iv = b"0102030405060708"
    
    # 创建AES加密器：CBC模式，使用固定IV
    cipher = AES.new(key.encode('utf-8'), AES.MODE_CBC, iv)
    
    # 对数据进行PKCS7填充，确保长度是16的倍数
    padded_data = pad(data.encode('utf-8'), AES.block_size)
    
    # 执行加密并进行Base64编码
    return b64encode(cipher.encrypt(padded_data)).decode('utf-8')


def rsa_encrypt(text: str, pub_key: str, modulus: str) -> str:
    """
    RSA加密函数
    =============
    
    使用RSA公钥对文本进行加密，主要用于加密AES密钥。
    网易云使用特殊的RSA加密方式：先反转字节序，再进行数学运算。
    
    加密流程:
    1. 将文本转换为UTF-8字节
    2. 反转字节序（这是网易云的特殊要求）
    3. 将字节转换为十六进制整数
    4. 执行RSA数学运算: (text^e) mod n
    5. 将结果格式化为256位十六进制字符串
    
    Args:
        text (str): 要加密的文本（通常是AES密钥）
        pub_key (str): RSA公钥指数（十六进制）
        modulus (str): RSA模数（十六进制）
        
    Returns:
        str: 256位十六进制加密结果
        
    Example:
        >>> encrypted = rsa_encrypt("secret", "010001", "abc123...")
        >>> len(encrypted)
        256
    
    Security Note:
        这个实现遵循网易云的特定协议，包括字节反转等特殊处理。
        不要用于其他RSA加密场景。
    """
    # 步骤1: 将文本转换为UTF-8字节并反转
    # 反转是网易云API的特殊要求，不是标准RSA流程
    text_reversed_bytes = text.encode('utf-8')[::-1]
    
    # 步骤2: 将反转后的字节转换为十六进制整数
    text_int = int(text_reversed_bytes.hex(), 16)
    
    # 步骤3: 执行RSA数学运算 (m^e) mod n
    # pow(base, exp, mod) 是Python内置的模幂运算
    encrypted_int = pow(text_int, int(pub_key, 16), int(modulus, 16))
    
    # 步骤4: 将结果格式化为256位十六进制字符串
    # zfill(256)确保结果长度为256位，不足时前面补0
    return format(encrypted_int, 'x').zfill(256)


def get_weapi_params(payload: dict) -> Dict[str, str]:
    """
    获取weapi加密参数 - 主要加密接口
    ==================================
    
    这是模块的主要接口函数，实现完整的网易云weapi加密流程。
    将原始的请求数据转换为网易云API所需的加密格式。
    
    完整加密流程:
    1. 将payload转换为JSON字符串
    2. 生成16位随机AES密钥
    3. 使用固定nonce进行第一次AES加密
    4. 使用随机密钥进行第二次AES加密
    5. 使用RSA公钥加密随机密钥
    6. 返回加密后的params和encSecKey
    
    Args:
        payload (dict): 要发送给API的原始数据字典
                       例如: {"id": "123456", "n": 100000}
        
    Returns:
        Dict[str, str]: 包含加密参数的字典
            - params: 双重AES加密后的数据
            - encSecKey: RSA加密后的密钥
            
    Example:
        >>> data = {"id": "123456", "n": 100000}
        >>> result = get_weapi_params(data)
        >>> "params" in result and "encSecKey" in result
        True
        >>> len(result["encSecKey"])
        256
    
    Usage in API requests:
        >>> import requests
        >>> data = {"id": "123456"}
        >>> encrypted = get_weapi_params(data)
        >>> response = requests.post(api_url, data=encrypted)
    
    Security Features:
        - 双层AES加密确保数据安全
        - 随机密钥防止重放攻击
        - RSA加密保护密钥传输
        - 符合网易云官方加密标准
    """
    # 步骤1: 将字典转换为JSON字符串
    # 确保数据格式符合API要求
    payload_str = json.dumps(payload)
    
    # 步骤2: 生成16位随机AES密钥
    # 每次请求都使用不同的密钥，增强安全性
    secret_key = generate_random_key(16)
    
    # 步骤3: 第一次AES加密 - 使用固定nonce
    # 这是网易云加密的第一层保护
    params = aes_encrypt(payload_str, NONCE)
    
    # 步骤4: 第二次AES加密 - 使用随机密钥
    # 这是网易云加密的第二层保护
    params = aes_encrypt(params, secret_key)
    
    # 步骤5: RSA加密随机密钥
    # 保护密钥在传输过程中的安全
    enc_sec_key = rsa_encrypt(secret_key, PUBKEY, MODULUS)
    
    # 步骤6: 返回加密结果
    # 这两个参数将作为POST数据发送给API
    return {
        "params": params,        # 双重加密后的数据
        "encSecKey": enc_sec_key # RSA加密后的密钥
    }