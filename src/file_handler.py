# -*- coding: utf-8 -*-
"""
文件处理模块
负责文件解压、音频处理、临时文件管理等
"""

import shutil
import subprocess
import time
import zipfile
from pathlib import Path
from typing import List

from .config import AUDIO_EXTENSIONS
from .utils import print_info, print_success, print_warn, ProcessingError


def process_downloaded_file(download_path: Path, music_dir: Path, temp_dir: Path) -> None:
    """
    处理下载的文件
    
    Args:
        download_path: 下载文件路径
        music_dir: 音乐目录
        temp_dir: 临时目录
    """
    extract_dir = temp_dir / download_path.stem
    
    print_info(f"开始解压: {download_path.name} -> {extract_dir.name}")
    
    # 解压ZIP文件
    try:
        with zipfile.ZipFile(download_path, 'r') as zip_ref:
            zip_ref.extractall(extract_dir)
    except zipfile.BadZipFile:
        raise ProcessingError(f"文件 {download_path.name} 不是有效的ZIP文件")
    except Exception as e:
        raise ProcessingError(f"解压文件失败: {e}")
    
    # 删除原始ZIP文件
    download_path.unlink()
    
    # 后处理文件夹
    post_process_folder(extract_dir, music_dir)


def post_process_folder(extract_folder: Path, music_dir: Path) -> None:
    """
    后处理解压后的文件夹
    
    Args:
        extract_folder: 解压文件夹路径
        music_dir: 音乐目录
    """
    print_info(f"开始后处理文件夹: {extract_folder}")
    
    try:
        # 查找FLAC文件
        flac_files = list(extract_folder.glob('*.flac'))
        if not flac_files:
            raise ProcessingError(f"在 {extract_folder} 中未找到 .flac 文件")

        flac_path = flac_files[0]
        song_title = flac_path.stem

        # 确保音乐目录存在
        music_dir.mkdir(exist_ok=True)
        output_path = music_dir / f"{song_title}.flac"

        # 构建FFmpeg命令
        ffmpeg_cmd = ['ffmpeg', '-y', '-i', str(flac_path)]

        # 查找封面图片
        jpg_files = (
            list(extract_folder.glob('*.jpg')) + 
            list(extract_folder.glob('*.jpeg')) + 
            list(extract_folder.glob('*.png'))
        )
        lrc_files = list(extract_folder.glob('*.lrc'))

        if jpg_files:
            ffmpeg_cmd.extend(['-i', str(jpg_files[0])])

        ffmpeg_cmd.extend(['-map_metadata', '-1', '-map', '0:a'])

        if jpg_files:
            ffmpeg_cmd.extend(['-map', '1:v'])

        ffmpeg_cmd.extend(['-c:a', 'copy'])

        if jpg_files:
            ffmpeg_cmd.extend(['-c:v', 'copy', '-disposition:v', 'attached_pic'])

        # 处理歌词
        if lrc_files:
            try:
                with open(lrc_files[0], 'r', encoding='utf-8') as f:
                    lyrics_content = f.read().replace('=', '\\=').replace(';', '\\;')
                ffmpeg_cmd.extend(['-metadata', f'lyrics={lyrics_content}'])
            except Exception as e:
                print_warn(f"警告: 读取歌词文件 {lrc_files[0].name} 失败: {e}")

        ffmpeg_cmd.append(str(output_path))

        print_info("正在执行FFmpeg命令...")
        result = subprocess.run(
            ffmpeg_cmd, 
            check=True, 
            capture_output=True, 
            text=True, 
            encoding='utf-8'
        )

        print_success(f"🎵 文件处理完成: {output_path.name}")
        
        # 清理解压目录
        shutil.rmtree(extract_folder)

    except FileNotFoundError:
        raise ProcessingError("错误: 未找到 'ffmpeg' 命令。请确保 FFmpeg 已安装并已添加到系统环境变量 PATH 中")
    except subprocess.CalledProcessError as e:
        raise ProcessingError(f"错误: ffmpeg 处理 {song_title} 时出错: {e.stderr}")
    except Exception as e:
        raise ProcessingError(f"错误: 后处理 {extract_folder} 时发生未知错误: {e}")


def is_download_complete(file_path: Path) -> bool:
    """
    检查文件是否下载完成
    
    Args:
        file_path: 文件路径
        
    Returns:
        是否下载完成
    """
    try:
        # 检查文件是否存在
        if not file_path.exists():
            return False
        
        initial_size = file_path.stat().st_size
        
        # 文件大小为0说明还在下载或下载失败
        if initial_size == 0:
            return False
        
        # 文件太小可能是下载失败或还在下载
        if initial_size < 1000:
            return False
        
        # 等待一秒后再次检查文件大小
        time.sleep(1)
        
        # 如果文件被删除了，说明下载失败
        if not file_path.exists():
            return False
        
        final_size = file_path.stat().st_size
        
        # 文件大小没有变化且大于最小阈值，认为下载完成
        return initial_size == final_size and final_size > 1000
        
    except Exception:
        # 任何异常都认为下载未完成
        return False


def find_audio_files_recursively(directory: Path) -> List[Path]:
    """
    递归搜索目录中的所有音频文件
    
    Args:
        directory: 搜索目录
        
    Returns:
        音频文件路径列表
    """
    audio_files = []
    
    try:
        for file_path in directory.rglob("*"):
            if file_path.is_file() and file_path.suffix.lower() in AUDIO_EXTENSIONS:
                audio_files.append(file_path)
    except Exception as e:
        print_warn(f"警告: 搜索音频文件时出错: {e}")
    
    return audio_files


def process_failed_downloads(temp_dir: Path, music_dir: Path) -> None:
    """
    处理临时目录中的失败下载文件
    
    Args:
        temp_dir: 临时目录
        music_dir: 音乐目录
    """
    if not temp_dir.exists():
        return

    print_info("🔍 检查临时目录中的音频文件...")
    audio_files = find_audio_files_recursively(temp_dir)

    if not audio_files:
        print_info("临时目录中未发现可用的音频文件")
        return

    print_success(f"发现 {len(audio_files)} 个音频文件，正在处理...")

    # 确保目标目录存在
    music_dir.mkdir(exist_ok=True)

    processed_count = 0
    for audio_file in audio_files:
        try:
            # 生成唯一的文件名避免冲突
            target_name = f"recovered_{audio_file.name}"
            target_path = music_dir / target_name

            # 如果目标文件已存在，添加数字后缀
            counter = 1
            while target_path.exists():
                name_parts = audio_file.stem, counter, audio_file.suffix
                target_name = f"recovered_{name_parts[0]}_{name_parts[1]}{name_parts[2]}"
                target_path = music_dir / target_name
                counter += 1

            # 移动文件
            shutil.move(str(audio_file), str(target_path))
            print_success(f"✓ 已恢复: {target_name}")
            processed_count += 1

        except Exception as e:
            print_warn(f"警告: 无法处理文件 {audio_file.name}: {e}")

    if processed_count > 0:
        print_success(f"🎉 成功恢复 {processed_count} 个音频文件到音乐目录")


def handle_temp_directory_cleanup(temp_dir: Path, music_dir: Path) -> None:
    """
    处理临时目录的最终清理
    
    Args:
        temp_dir: 临时目录
        music_dir: 音乐目录
    """
    if not temp_dir.exists():
        return

    # 先尝试恢复音频文件
    process_failed_downloads(temp_dir, music_dir)

    # 检查是否还有未处理的文件
    remaining_files = []
    try:
        for item in temp_dir.rglob("*"):
            if item.is_file():
                remaining_files.append(item)
    except:
        pass

    if remaining_files:
        # 将整个temp目录移动到用户指定的目录
        temp_backup_path = music_dir / "temp_downloads_backup"
        try:
            if temp_backup_path.exists():
                shutil.rmtree(temp_backup_path)
            shutil.move(str(temp_dir), str(temp_backup_path))
            print_warn(f"⚠️  临时文件已备份到: {temp_backup_path}")
            print_info("请手动检查该目录中是否有需要的文件")
        except Exception as e:
            print_warn(f"错误: 无法备份临时文件: {e}")
    else:
        # 安全删除空的临时目录
        try:
            shutil.rmtree(temp_dir)
            print_info("✓ 临时目录已清理")
        except:
            pass


def cleanup_temp_directory(temp_dir: Path, music_dir: Path) -> None:
    """
    清理临时目录
    
    Args:
        temp_dir: 临时目录
        music_dir: 音乐目录
    """
    if temp_dir.exists():
        try:
            has_files = any(temp_dir.iterdir())
            if has_files:
                print_warn(f"警告: 临时目录 '{temp_dir}' 不为空，可能包含处理失败任务的残留文件")
            else:
                shutil.rmtree(temp_dir)
        except:
            pass