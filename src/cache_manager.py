# -*- coding: utf-8 -*-
"""
缓存管理模块
提供智能的数据缓存和管理功能
"""

import json
from dataclasses import dataclass, asdict
from typing import Any, Dict, Optional, Union
import hashlib

from .utils import print_debug, print_info
from .logging_utils import get_logger


@dataclass
class CacheEntry:
    """缓存条目"""
    data: Any
    timestamp: float
    ttl: float  # 生存时间（秒）
    access_count: int = 0
    last_access: float = 0.0

    def is_expired(self) -> bool:
        """检查是否过期"""
        return time.time() - self.timestamp > self.ttl

    def is_valid(self) -> bool:
        """检查是否有效"""
        return not self.is_expired()

    def touch(self) -> None:
        """更新访问时间"""
        self.access_count += 1
        self.last_access = time.time()


class CacheManager:
    """缓存管理器"""

    def __init__(self, cache_dir: Optional[Path] = None, max_size: int = 1000):
        self.logger = get_logger()
        self.cache_dir = cache_dir or Path.home() / ".nt_dl_cache"
        self.max_size = max_size
        self.memory_cache: Dict[str, CacheEntry] = {}

        # 确保缓存目录存在
        self.cache_dir.mkdir(exist_ok=True)

        # 加载持久化缓存
        self._load_persistent_cache()

    def _generate_key(self, key: Union[str, Dict]) -> str:
        """生成缓存键"""
        if isinstance(key, dict):
            # 对字典进行排序后生成哈希
            sorted_key = json.dumps(key, sort_keys=True)
            return hashlib.sha256(sorted_key.encode()).hexdigest()
        return str(key)

    def set(self, key: Union[str, Dict], data: Any, ttl: float = 3600) -> None:
        """设置缓存"""
        cache_key = self._generate_key(key)

        entry = CacheEntry(
            data=data,
            timestamp=time.time(),
            ttl=ttl
        )

        self.memory_cache[cache_key] = entry

        # 检查缓存大小限制
        if len(self.memory_cache) > self.max_size:
            self._evict_old_entries()

        # 持久化重要缓存
        if ttl > 3600:  # 超过1小时的缓存进行持久化
            self._save_to_disk(cache_key, entry)

        self.logger.debug(f"缓存设置: {cache_key[:8]}... (TTL: {ttl}s)")

    def get(self, key: Union[str, Dict]) -> Optional[Any]:
        """获取缓存"""
        cache_key = self._generate_key(key)

        # 先检查内存缓存
        if cache_key in self.memory_cache:
            entry = self.memory_cache[cache_key]
            if entry.is_valid():
                entry.touch()
                self.logger.debug(f"内存缓存命中: {cache_key[:8]}...")
                return entry.data
            else:
                # 过期缓存清理
                del self.memory_cache[cache_key]
                self.logger.debug(f"内存缓存过期: {cache_key[:8]}...")

        # 检查磁盘缓存
        disk_entry = self._load_from_disk(cache_key)
        if disk_entry and disk_entry.is_valid():
            # 加载到内存缓存
            self.memory_cache[cache_key] = disk_entry
            disk_entry.touch()
            self.logger.debug(f"磁盘缓存命中: {cache_key[:8]}...")
            return disk_entry.data

        return None

    def delete(self, key: Union[str, Dict]) -> bool:
        """删除缓存"""
        cache_key = self._generate_key(key)

        # 删除内存缓存
        memory_deleted = cache_key in self.memory_cache
        if memory_deleted:
            del self.memory_cache[cache_key]

        # 删除磁盘缓存
        disk_file = self.cache_dir / f"{cache_key}.json"
        disk_deleted = False
        if disk_file.exists():
            disk_file.unlink()
            disk_deleted = True

        if memory_deleted or disk_deleted:
            self.logger.debug(f"缓存删除: {cache_key[:8]}...")
            return True

        return False

    def clear(self) -> None:
        """清空所有缓存"""
        # 清空内存缓存
        self.memory_cache.clear()

        # 清空磁盘缓存
        for cache_file in self.cache_dir.glob("*.json"):
            cache_file.unlink()

        self.logger.info("所有缓存已清空")

    def cleanup_expired(self) -> int:
        """清理过期缓存"""
        expired_count = 0

        # 清理内存缓存
        expired_keys = [
            key for key, entry in self.memory_cache.items()
            if entry.is_expired()
        ]

        for key in expired_keys:
            del self.memory_cache[key]
            expired_count += 1

        # 清理磁盘缓存
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                entry = CacheEntry(**data)
                if entry.is_expired():
                    cache_file.unlink()
                    expired_count += 1
            except Exception:
                # 损坏的缓存文件也删除
                cache_file.unlink()
                expired_count += 1

        if expired_count > 0:
            self.logger.info(f"清理了 {expired_count} 个过期缓存")

        return expired_count

    def get_stats(self) -> Dict:
        """获取缓存统计信息"""
        memory_count = len(self.memory_cache)
        disk_count = len(list(self.cache_dir.glob("*.json")))

        total_access = sum(entry.access_count for entry in self.memory_cache.values())

        return {
            'memory_entries': memory_count,
            'disk_entries': disk_count,
            'total_entries': memory_count + disk_count,
            'total_access_count': total_access,
            'cache_dir': str(self.cache_dir),
            'max_size': self.max_size
        }

    def _evict_old_entries(self) -> None:
        """淘汰旧的缓存条目"""
        if len(self.memory_cache) <= self.max_size:
            return

        # 按最后访问时间排序，删除最旧的条目
        sorted_entries = sorted(
            self.memory_cache.items(),
            key=lambda x: x[1].last_access or x[1].timestamp
        )

        # 删除最旧的25%条目
        evict_count = len(self.memory_cache) - self.max_size + self.max_size // 4

        for i in range(evict_count):
            if i < len(sorted_entries):
                key = sorted_entries[i][0]
                del self.memory_cache[key]

        self.logger.debug(f"淘汰了 {evict_count} 个旧缓存条目")

    def _save_to_disk(self, key: str, entry: CacheEntry) -> None:
        """保存缓存到磁盘"""
        try:
            cache_file = self.cache_dir / f"{key}.json"
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(entry), f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.warning(f"保存缓存到磁盘失败: {e}")

    def _load_from_disk(self, key: str) -> Optional[CacheEntry]:
        """从磁盘加载缓存"""
        try:
            cache_file = self.cache_dir / f"{key}.json"
            if cache_file.exists():
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                return CacheEntry(**data)
        except Exception as e:
            self.logger.warning(f"从磁盘加载缓存失败: {e}")

        return None

    def _load_persistent_cache(self) -> None:
        """加载持久化缓存"""
        try:
            cache_files = list(self.cache_dir.glob("*.json"))
            loaded_count = 0

            for cache_file in cache_files:
                try:
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    entry = CacheEntry(**data)
                    if entry.is_valid():
                        key = cache_file.stem
                        self.memory_cache[key] = entry
                        loaded_count += 1
                    else:
                        # 删除过期的磁盘缓存
                        cache_file.unlink()
                except Exception:
                    # 删除损坏的缓存文件
                    cache_file.unlink()

            if loaded_count > 0:
                self.logger.debug(f"加载了 {loaded_count} 个持久化缓存")

        except Exception as e:
            self.logger.warning(f"加载持久化缓存失败: {e}")


# 全局缓存管理器实例
cache_manager = CacheManager()
