# -*- coding: utf-8 -*-
"""
下载模块
负责浏览器自动化和文件下载
"""

import asyncio
import random
from typing import Dict, List

from playwright.async_api import async_playwright, Error as PlaywrightError

from .config import DOWNLOAD_TIMEOUT, MAX_RETRIES, TARGET_PAGE_URL
from .file_handler import process_downloaded_file, is_download_complete
from .pyinstaller_compat import is_packaged_environment, get_browser_executable_path
from .ui import create_lightweight_task_indicator
from .utils import print_debug, print_error, print_info, print_success, print_warn, ProcessingError


async def execute_lightweight_download_workflow(
    urls: List[str],
    quality: str,
    temp_dir: Path,
    music_dir: Path,
    browser_data_dir: Path
) -> Dict[str, str]:
    """
    执行轻量级下载工作流

    Args:
        urls: URL列表
        quality: 音质选择
        temp_dir: 临时目录
        music_dir: 音乐目录
        browser_data_dir: 浏览器数据目录

    Returns:
        失败任务字典
    """
    failed_tasks: Dict[str, str] = {}

    # 主重试循环
    for attempt in range(MAX_RETRIES + 1):
        urls_remaining = urls if attempt == 0 else list(failed_tasks.keys())

        if not urls_remaining:
            break

        if attempt > 0:
            print_warn(f"\n--- 开始重试失败的任务 (第 {attempt}/{MAX_RETRIES} 次重试) ---")
            await asyncio.sleep(3)

        print_info(f"📋 开始处理 {len(urls_remaining)} 个任务，串行模式确保稳定性")

        # 轻量级处理 - 减少进度更新频率
        next_round_failures = {}
        for index, url in enumerate(urls_remaining):
            # 只在关键节点更新进度信息
            if index % 5 == 0 or index == len(urls_remaining) - 1:
                print_info(f"正在处理第 {index + 1}/{len(urls_remaining)} 个任务")

            try:
                await process_single_url_lightweight(
                    url, quality, index + 1, len(urls_remaining),
                    temp_dir, music_dir, browser_data_dir
                )
                print_success(f"✅ 成功: 第{index + 1}/{len(urls_remaining)}个任务")
                failed_tasks.pop(url, None)

            except ProcessingError as e:
                print_error(f"❌ 失败: 第{index + 1}/{len(urls_remaining)}个任务")
                print_error(f"   错误: {e.message}")
                next_round_failures[url] = e.message

            # 适度延迟，避免过于频繁
            delay = random.randint(1500, 3000) / 1000
            await asyncio.sleep(delay)

        failed_tasks.update(next_round_failures)

    return failed_tasks


async def process_single_url_lightweight(
    url: str,
    quality_text: str,
    current: int,
    total: int,
    temp_dir: Path,
    music_dir: Path,
    browser_data_dir: Path
) -> None:
    """
    轻量级单任务处理

    Args:
        url: 歌曲URL
        quality_text: 音质文本
        current: 当前任务编号
        total: 总任务数
        temp_dir: 临时目录
        music_dir: 音乐目录
        browser_data_dir: 浏览器数据目录
    """
    # 显示简洁的任务信息
    print(create_lightweight_task_indicator(url, current, total))

    # 环境准备
    temp_dir.mkdir(exist_ok=True)

    async with async_playwright() as p:
        # 准备浏览器启动参数
        launch_args = [
            "--no-sandbox",
            "--disable-setuid-sandbox",
            "--disable-dev-shm-usage",
            "--disable-gpu",
            "--no-first-run",
            "--disable-web-security",
            "--enable-automation",
            "--allow-running-insecure-content",
            "--disable-background-timer-throttling",
            "--disable-renderer-backgrounding",
            "--disable-backgrounding-occluded-windows",
            "--disable-blink-features=AutomationControlled",
            "--disable-extensions",
            "--no-default-browser-check",
            "--disable-popup-blocking",
            "--disable-translate",
            "--disable-default-apps",
            "--disable-sync",
            "--no-pings",
            "--allow-downloads",
            "--disable-component-updates",
            "--disable-background-networking",
            "--disable-client-side-phishing-detection",
        ]

        # 准备启动参数
        launch_kwargs = {
            "user_data_dir": str(browser_data_dir.absolute()),
            "headless": True,
            "accept_downloads": True,
            "downloads_path": str(temp_dir.absolute()),
            "args": launch_args
        }

        # 在打包环境中，如果有自定义浏览器路径，则使用它
        if is_packaged_environment():
            executable_path = get_browser_executable_path()
            if executable_path:
                launch_kwargs["executable_path"] = executable_path
                print_debug(f"使用自定义浏览器路径: {executable_path}")
            else:
                print_debug("使用环境变量指定的浏览器路径")

        # 使用 launch_persistent_context 来管理用户数据目录
        context = await p.chromium.launch_persistent_context(**launch_kwargs)

        try:
            page = await context.new_page()
            download_future = asyncio.Future()

            async def on_download(download):
                try:
                    zip_path = temp_dir / download.suggested_filename
                    print_info(f"触发下载事件，开始保存文件到 -> {zip_path}")
                    await download.save_as(zip_path)
                    print_success(f"文件已保存: {zip_path.name}")
                    download_future.set_result(zip_path)
                except Exception as e:
                    download_future.set_exception(ProcessingError(f"保存下载文件时失败: {e}"))

            page.on("download", on_download)

            try:
                # 导航到目标页面
                await page.goto(TARGET_PAGE_URL, timeout=60000)
                await page.wait_for_selector("h5:has-text('网易云无损解析')", state='visible', timeout=15000)

                # 填写URL
                await page.locator("[placeholder=\"请输入网易云音乐链接\"]").fill(url)

                # 选择音质
                await page.locator(".ep-select__wrapper").first.click()
                try:
                    await page.locator(f"li:has-text('{quality_text}')").click(timeout=3000)
                except PlaywrightError:
                    print_warn(f"音质 '{quality_text}' 不可用，自动为 {url} 降级到 '高解析度无损(VIP)'")
                    await page.locator("li:has-text('高解析度无损(VIP)')").click()

                # 选择下载类型
                await page.locator(".ep-select__wrapper").nth(1).click()
                await page.locator("li:has-text('单曲')").click()

                # 点击解析
                await page.locator("button:has-text('立即解析')").click()
                await page.locator("button:has-text('确认')").click()

                # 等待下载按钮
                download_button = page.locator("button:has-text('点击下载')")
                await download_button.wait_for(state='visible', timeout=60000)
                await download_button.click()

                # 等待下载完成
                zip_path = await asyncio.wait_for(download_future, timeout=DOWNLOAD_TIMEOUT)

                # 处理文件
                process_downloaded_file(zip_path, music_dir, temp_dir)

            finally:
                page.remove_listener("download", on_download)
                await page.close()

        finally:
            await context.close()
