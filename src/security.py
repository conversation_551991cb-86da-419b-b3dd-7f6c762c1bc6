# -*- coding: utf-8 -*-
"""
安全模块 - 配置加密和安全管理
===============================

本模块提供配置加密、密钥管理和安全相关的功能。
采用现代加密算法和安全最佳实践，保护敏感配置信息。

主要功能:
- 配置文件加密/解密
- 密钥安全生成和存储
- 敏感信息保护
- 安全配置验证

技术实现:
- 使用 cryptography 库的 Fernet 对称加密
- 基于 PBKDF2 的密钥派生
- 安全的随机数生成
- 内存安全的密钥处理

作者: nt_dl_team
版本: v2.3.0

⚠️ 安全声明:
本模块中的所有加密实现均基于公开的密码学标准和最佳实践。
- 加密算法: 采用业界标准的对称加密算法
- 密钥管理: 遵循NIST密钥管理指导原则
- 安全实现: 基于OWASP安全开发指南
- 使用限制: 仅供技术学习和研究使用
- 责任声明: 加密安全性由用户自行评估和承担风险
"""

import json
import base64
import hashlib
import secrets
from typing import Dict, Any, Optional, Union
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from .logging_utils import get_logger
from .utils import print_error, print_info, print_warn

logger = get_logger(__name__)


class ConfigEncryption:
    """
    配置加密管理器

    提供配置文件的加密存储和安全访问功能。
    使用 Fernet 对称加密算法，支持密钥派生和安全存储。
    """

    def __init__(self, key_file: str = ".config_key", salt_file: str = ".config_salt"):
        """
        初始化配置加密管理器

        Args:
            key_file: 密钥文件路径
            salt_file: 盐值文件路径
        """
        self.key_file = Path(key_file)
        self.salt_file = Path(salt_file)
        self.cipher: Optional[Fernet] = None
        self._initialize_encryption()

    def _initialize_encryption(self) -> None:
        """初始化加密组件"""
        try:
            # 获取或生成盐值
            salt = self._get_or_create_salt()

            # 生成或加载密钥
            key = self._derive_key_from_password(salt)

            # 初始化加密器
            self.cipher = Fernet(key)

            logger.info("配置加密初始化成功")

        except Exception as e:
            logger.error(f"配置加密初始化失败: {e}")
            print_error(f"安全模块初始化失败: {e}")
            raise

    def _get_or_create_salt(self) -> bytes:
        """
        获取或创建加密盐值

        Returns:
            32字节的随机盐值
        """
        if self.salt_file.exists():
            try:
                with open(self.salt_file, 'rb') as f:
                    salt = f.read()
                if len(salt) == 32:  # 验证盐值长度
                    return salt
                else:
                    print_warn("盐值文件损坏，重新生成")
            except Exception as e:
                print_warn(f"读取盐值文件失败: {e}，重新生成")

        # 生成新的盐值
        salt = secrets.token_bytes(32)
        try:
            with open(self.salt_file, 'wb') as f:
                f.write(salt)
            # 设置文件权限（仅所有者可读写）
            os.chmod(self.salt_file, 0o600)
            logger.info("新盐值文件创建成功")
        except Exception as e:
            logger.error(f"保存盐值文件失败: {e}")
            raise

        return salt

    def _derive_key_from_password(self, salt: bytes, password: Optional[str] = None) -> bytes:
        """
        从密码派生加密密钥

        Args:
            salt: 加密盐值
            password: 用户密码（可选，默认使用系统信息）

        Returns:
            派生的32字节密钥
        """
        if password is None:
            # 使用系统信息作为默认密码
            password = self._generate_system_password()

        # 使用 PBKDF2 进行密钥派生
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,  # 10万次迭代，平衡安全性和性能
        )

        key = base64.urlsafe_b64encode(kdf.derive(password.encode('utf-8')))
        return key

    def _generate_system_password(self) -> str:
        """
        生成基于系统信息的默认密码

        Returns:
            系统特征密码字符串
        """
        # 收集系统特征信息
        system_info = [
            os.getcwd(),  # 当前工作目录
            str(Path.home()),  # 用户主目录
            os.environ.get('USER', os.environ.get('USERNAME', 'unknown')),  # 用户名
        ]

        # 生成特征哈希
        combined = '|'.join(system_info)
        return hashlib.sha256(combined.encode('utf-8')).hexdigest()

    def encrypt_config(self, config: Dict[str, Any]) -> str:
        """
        加密配置字典

        Args:
            config: 要加密的配置字典

        Returns:
            Base64编码的加密配置字符串

        Raises:
            ValueError: 加密失败
        """
        if self.cipher is None:
            raise ValueError("加密器未初始化")

        try:
            # 序列化配置
            config_json = json.dumps(config, ensure_ascii=False, sort_keys=True)

            # 加密配置
            encrypted_bytes = self.cipher.encrypt(config_json.encode('utf-8'))

            # Base64编码
            encrypted_b64 = base64.b64encode(encrypted_bytes).decode('ascii')

            logger.info("配置加密成功")
            return encrypted_b64

        except Exception as e:
            logger.error(f"配置加密失败: {e}")
            raise ValueError(f"配置加密失败: {e}")

    def decrypt_config(self, encrypted_config: str) -> Dict[str, Any]:
        """
        解密配置字符串

        Args:
            encrypted_config: Base64编码的加密配置

        Returns:
            解密后的配置字典

        Raises:
            ValueError: 解密失败
        """
        if self.cipher is None:
            raise ValueError("加密器未初始化")

        try:
            # Base64解码
            encrypted_bytes = base64.b64decode(encrypted_config.encode('ascii'))

            # 解密配置
            decrypted_bytes = self.cipher.decrypt(encrypted_bytes)

            # 反序列化配置
            config = json.loads(decrypted_bytes.decode('utf-8'))

            logger.info("配置解密成功")
            return config

        except Exception as e:
            logger.error(f"配置解密失败: {e}")
            raise ValueError(f"配置解密失败: {e}")

    def is_encrypted_config(self, config_data: str) -> bool:
        """
        检查配置数据是否已加密

        Args:
            config_data: 配置数据字符串

        Returns:
            True if 已加密, False otherwise
        """
        try:
            # 尝试Base64解码
            base64.b64decode(config_data.encode('ascii'))

            # 尝试解密（不抛出异常）
            self.decrypt_config(config_data)
            return True

        except Exception:
            return False

    def cleanup_key_files(self) -> None:
        """
        清理密钥文件（用于重置）

        ⚠️ 警告: 此操作将删除所有加密密钥，已加密的配置将无法恢复
        """
        try:
            if self.key_file.exists():
                self.key_file.unlink()
                logger.info("密钥文件已删除")

            if self.salt_file.exists():
                self.salt_file.unlink()
                logger.info("盐值文件已删除")

            print_info("加密密钥已重置，请重新配置")

        except Exception as e:
            logger.error(f"清理密钥文件失败: {e}")
            print_error(f"清理密钥文件失败: {e}")


class SecureConfigManager:
    """
    安全配置管理器

    提供配置的安全存储、加载和管理功能。
    支持敏感信息的加密存储和安全访问。
    """

    def __init__(self, config_file: str = "config.encrypted"):
        """
        初始化安全配置管理器

        Args:
            config_file: 配置文件路径
        """
        self.config_file = Path(config_file)
        self.encryption = ConfigEncryption()
        self._config_cache: Optional[Dict[str, Any]] = None

    def save_config(self, config: Dict[str, Any]) -> None:
        """
        保存加密配置

        Args:
            config: 要保存的配置字典
        """
        try:
            # 加密配置
            encrypted_config = self.encryption.encrypt_config(config)

            # 保存到文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                f.write(encrypted_config)

            # 设置文件权限
            os.chmod(self.config_file, 0o600)

            # 更新缓存
            self._config_cache = config.copy()

            print_info("配置保存成功")
            logger.info(f"配置已保存到 {self.config_file}")

        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            print_error(f"保存配置失败: {e}")
            raise

    def load_config(self) -> Dict[str, Any]:
        """
        加载解密配置

        Returns:
            配置字典
        """
        # 检查缓存
        if self._config_cache is not None:
            return self._config_cache

        if not self.config_file.exists():
            logger.warning("配置文件不存在，返回默认配置")
            return {}

        try:
            # 读取配置文件
            with open(self.config_file, 'r', encoding='utf-8') as f:
                encrypted_config = f.read().strip()

            # 解密配置
            config = self.encryption.decrypt_config(encrypted_config)

            # 缓存配置
            self._config_cache = config

            logger.info("配置加载成功")
            return config

        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            print_error(f"加载配置失败: {e}")
            return {}

    def get_config_value(self, key: str, default: Any = None) -> Any:
        """
        获取配置值

        Args:
            key: 配置键名
            default: 默认值

        Returns:
            配置值
        """
        config = self.load_config()
        return config.get(key, default)

    def set_config_value(self, key: str, value: Any) -> None:
        """
        设置配置值

        Args:
            key: 配置键名
            value: 配置值
        """
        config = self.load_config()
        config[key] = value
        self.save_config(config)

    def clear_cache(self) -> None:
        """清理配置缓存"""
        self._config_cache = None
        logger.info("配置缓存已清理")


# 全局安全配置管理器实例
secure_config = SecureConfigManager()
