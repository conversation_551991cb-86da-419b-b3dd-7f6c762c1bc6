# -*- coding: utf-8 -*-
"""
配置验证模块
验证和自动修复配置问题
"""

import re
from pathlib import Path
from typing import Dict, List, Tuple

from .config import (
    CONCURRENT_LIMIT, MAX_RETRIES, DOWNLOAD_TIMEOUT,
    TARGET_PAGE_URL, MODULUS, NONCE, PUBKEY, HEADERS, AUDIO_EXTENSIONS
)
from .utils import print_warn, print_success, print_error


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.issues: List[Tuple[str, str, str]] = []  # (category, issue, suggestion)
    
    def validate_all(self) -> bool:
        """验证所有配置"""
        self.issues.clear()
        
        # 验证各个配置项
        self._validate_download_config()
        self._validate_crypto_config()
        self._validate_network_config()
        self._validate_file_config()
        self._validate_dependencies()
        
        return len(self.issues) == 0
    
    def _validate_download_config(self) -> None:
        """验证下载配置"""
        # 验证并发限制
        if not isinstance(CONCURRENT_LIMIT, int) or CONCURRENT_LIMIT < 1 or CONCURRENT_LIMIT > 10:
            self.issues.append((
                "下载配置", 
                f"CONCURRENT_LIMIT值异常: {CONCURRENT_LIMIT}",
                "建议设置为1-10之间的整数"
            ))
        
        # 验证重试次数
        if not isinstance(MAX_RETRIES, int) or MAX_RETRIES < 0 or MAX_RETRIES > 10:
            self.issues.append((
                "下载配置",
                f"MAX_RETRIES值异常: {MAX_RETRIES}",
                "建议设置为0-10之间的整数"
            ))
        
        # 验证超时时间
        if not isinstance(DOWNLOAD_TIMEOUT, int) or DOWNLOAD_TIMEOUT < 60:
            self.issues.append((
                "下载配置",
                f"DOWNLOAD_TIMEOUT值异常: {DOWNLOAD_TIMEOUT}",
                "建议设置为至少60秒"
            ))
    
    def _validate_crypto_config(self) -> None:
        """验证加密配置"""
        # 验证MODULUS
        if not isinstance(MODULUS, str) or len(MODULUS) < 200:
            self.issues.append((
                "加密配置",
                "MODULUS长度不足",
                "检查MODULUS是否完整"
            ))
        
        # 验证NONCE
        if not isinstance(NONCE, str) or len(NONCE) != 16:
            self.issues.append((
                "加密配置",
                f"NONCE长度错误: {len(NONCE)}",
                "NONCE应该是16字符的字符串"
            ))
        
        # 验证PUBKEY
        if not isinstance(PUBKEY, str) or len(PUBKEY) != 6:
            self.issues.append((
                "加密配置",
                f"PUBKEY长度错误: {len(PUBKEY)}",
                "PUBKEY应该是6字符的字符串"
            ))
    
    def _validate_network_config(self) -> None:
        """验证网络配置"""
        # 验证目标URL
        if not isinstance(TARGET_PAGE_URL, str) or not TARGET_PAGE_URL.startswith("http"):
            self.issues.append((
                "网络配置",
                "TARGET_PAGE_URL格式错误",
                "URL应该以http或https开头"
            ))
        
        # 验证HTTP头
        required_headers = ['User-Agent', 'Referer', 'Content-Type']
        for header in required_headers:
            if header not in HEADERS:
                self.issues.append((
                    "网络配置",
                    f"缺少HTTP头: {header}",
                    f"添加{header}到HEADERS配置"
                ))
        
        # 验证User-Agent格式
        if 'User-Agent' in HEADERS:
            ua = HEADERS['User-Agent']
            if not re.search(r'Mozilla.*Chrome.*Safari', ua):
                self.issues.append((
                    "网络配置",
                    "User-Agent格式可能不标准",
                    "建议使用标准的浏览器User-Agent"
                ))
    
    def _validate_file_config(self) -> None:
        """验证文件配置"""
        # 验证音频扩展名
        expected_extensions = {".wav", ".mp3", ".m4a", ".flac", ".aac", ".ogg", ".opus", ".wma", ".aiff", ".alac"}
        
        if not isinstance(AUDIO_EXTENSIONS, set):
            self.issues.append((
                "文件配置",
                "AUDIO_EXTENSIONS类型错误",
                "应该是set类型"
            ))
        elif AUDIO_EXTENSIONS != expected_extensions:
            missing = expected_extensions - AUDIO_EXTENSIONS
            extra = AUDIO_EXTENSIONS - expected_extensions
            
            if missing:
                self.issues.append((
                    "文件配置",
                    f"缺少音频扩展名: {missing}",
                    "添加缺少的音频格式支持"
                ))
            
            if extra:
                self.issues.append((
                    "文件配置",
                    f"额外的音频扩展名: {extra}",
                    "检查是否需要这些额外格式"
                ))
    
    def _validate_dependencies(self) -> None:
        """验证依赖项"""
        # 检查requirements.txt
        req_file = Path("requirements.txt")
        if not req_file.exists():
            self.issues.append((
                "依赖配置",
                "requirements.txt文件不存在",
                "创建requirements.txt文件"
            ))
        else:
            try:
                with open(req_file, 'r') as f:
                    content = f.read()
                
                required_packages = ['requests', 'beautifulsoup4', 'pycryptodome', 'playwright']
                missing_packages = []
                
                for package in required_packages:
                    if package not in content:
                        missing_packages.append(package)
                
                if missing_packages:
                    self.issues.append((
                        "依赖配置",
                        f"requirements.txt缺少依赖: {missing_packages}",
                        "添加缺少的依赖包"
                    ))
            except Exception as e:
                self.issues.append((
                    "依赖配置",
                    f"读取requirements.txt失败: {e}",
                    "检查文件权限和格式"
                ))
    
    def display_issues(self) -> None:
        """显示配置问题"""
        if not self.issues:
            print_success("✅ 所有配置验证通过")
            return
        
        print_warn(f"⚠️ 发现 {len(self.issues)} 个配置问题:")
        print()
        
        # 按类别分组显示
        issues_by_category: Dict[str, List[Tuple[str, str]]] = {}
        for category, issue, suggestion in self.issues:
            if category not in issues_by_category:
                issues_by_category[category] = []
            issues_by_category[category].append((issue, suggestion))
        
        for category, category_issues in issues_by_category.items():
            print_error(f"🔧 {category}:")
            for issue, suggestion in category_issues:
                print(f"   ❌ {issue}")
                print(f"   💡 {suggestion}")
            print()
    
    def auto_fix_issues(self) -> int:
        """自动修复可修复的问题"""
        fixed_count = 0
        
        # 这里可以添加自动修复逻辑
        # 例如：创建缺失的requirements.txt文件
        
        req_file = Path("requirements.txt")
        if not req_file.exists():
            try:
                default_requirements = """# 网易云音乐下载器依赖包
# 基于Rust版本功能完整迁移

# HTTP请求
requests>=2.31.0

# HTML解析
beautifulsoup4>=4.12.0
lxml>=4.9.0

# 加密
pycryptodome>=3.19.0

# 浏览器自动化
playwright>=1.40.0
"""
                with open(req_file, 'w', encoding='utf-8') as f:
                    f.write(default_requirements)
                print_success("✅ 已自动创建 requirements.txt 文件")
                fixed_count += 1
            except Exception as e:
                print_error(f"❌ 自动创建 requirements.txt 失败: {e}")
        
        return fixed_count


def validate_config() -> bool:
    """验证配置的便捷函数"""
    validator = ConfigValidator()
    is_valid = validator.validate_all()
    
    if not is_valid:
        validator.display_issues()
        
        # 尝试自动修复
        fixed = validator.auto_fix_issues()
        if fixed > 0:
            print_success(f"✅ 已自动修复 {fixed} 个问题")
    
    return is_valid