# -*- coding: utf-8 -*-
"""
PyInstaller兼容性模块
=======================

本模块用于处理PyInstaller打包后的环境兼容性问题，特别是Playwright的浏览器依赖。

核心功能:
- 检测程序是否在打包环境（frozen）中运行
- 动态设置Playwright浏览器路径环境变量
- 提供浏览器可执行文件路径检测功能
- 验证浏览器安装的完整性

技术实现:
- 通过sys.frozen属性检测PyInstaller打包环境
- 自动配置PLAYWRIGHT_BROWSERS_PATH环境变量
- 支持跨平台的浏览器可执行文件路径检测
- 提供完整的浏览器安装验证机制

作者: nt_dl_team
版本: v2.2.0
"""

import platform
import sys

from .utils import print_debug, print_info, print_warn


def is_packaged_environment() -> bool:
    """
    检测当前是否在PyInstaller打包环境中运行

    Returns:
        bool: 如果在打包环境中运行返回True，否则返回False
    """
    return getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')


def get_browsers_path() -> Optional[Path]:
    """
    获取浏览器安装路径

    在打包环境中，返回与可执行文件同级的ms-playwright目录路径。
    在开发环境中，返回None（使用系统默认路径）。

    Returns:
        Optional[Path]: 浏览器路径，开发环境中返回None
    """
    if is_packaged_environment():
        application_path = Path(sys.executable).parent
        browsers_path = application_path / "ms-playwright"
        return browsers_path
    else:
        return None


def get_browser_executable_path() -> Optional[str]:
    """
    获取浏览器可执行文件的完整路径

    根据当前操作系统和环境，返回Chromium浏览器可执行文件的路径。
    仅在打包环境中有效，开发环境返回None。

    Returns:
        Optional[str]: 浏览器可执行文件路径，开发环境或文件不存在时返回None
    """
    browsers_path = get_browsers_path()
    if browsers_path is None:
        return None

    # 获取操作系统类型
    system = platform.system().lower()

    # 查找Chromium目录（版本号可能变化）
    chromium_dirs = list(browsers_path.glob("chromium-*"))
    if not chromium_dirs:
        print_warn(f"未找到Chromium目录: {browsers_path}")
        return None

    # 使用最新版本的Chromium
    chromium_dir = sorted(chromium_dirs)[-1]

    # 根据操作系统构建可执行文件路径
    if system == "darwin":  # macOS
        executable_path = chromium_dir / "chrome-mac" / "Chromium.app" / "Contents" / "MacOS" / "Chromium"
    elif system == "linux":
        executable_path = chromium_dir / "chrome-linux" / "chrome"
    elif system == "windows":
        executable_path = chromium_dir / "chrome-win" / "chrome.exe"
    else:
        print_warn(f"不支持的操作系统: {system}")
        return None

    # 检查文件是否存在
    if executable_path.exists():
        return str(executable_path)
    else:
        print_warn(f"浏览器可执行文件不存在: {executable_path}")
        return None


def verify_browser_installation() -> bool:
    """
    验证浏览器安装的完整性

    检查浏览器目录和可执行文件是否存在且可访问。

    Returns:
        bool: 如果浏览器安装完整返回True，否则返回False
    """
    if not is_packaged_environment():
        # 开发环境中，假设Playwright已正确安装
        print_debug("开发环境，跳过浏览器安装验证")
        return True

    browsers_path = get_browsers_path()
    if browsers_path is None or not browsers_path.exists():
        print_warn(f"浏览器目录不存在: {browsers_path}")
        return False

    executable_path = get_browser_executable_path()
    if executable_path is None:
        print_warn("无法获取浏览器可执行文件路径")
        return False

    executable_file = Path(executable_path)
    if not executable_file.exists():
        print_warn(f"浏览器可执行文件不存在: {executable_path}")
        return False

    # 检查文件权限（Unix系统）
    if platform.system() != "Windows":
        if not os.access(executable_path, os.X_OK):
            print_warn(f"浏览器可执行文件无执行权限: {executable_path}")
            return False

    print_debug(f"浏览器安装验证成功: {executable_path}")
    return True


def setup_pyinstaller_environment() -> bool:
    """
    检测并设置PyInstaller打包环境

    如果程序是通过PyInstaller打包的，此函数会执行以下操作：
    1. 获取主可执行文件的父目录作为应用程序的根路径
    2. 构建指向捆绑的浏览器目录的路径
    3. 将此路径设置为PLAYWRIGHT_BROWSERS_PATH环境变量

    这个操作必须在任何playwright模块被导入或使用之前完成。

    Returns:
        bool: 如果是在打包环境中运行，则返回True，否则返回False
    """
    if is_packaged_environment():
        # 检测到PyInstaller打包环境
        browsers_path = get_browsers_path()
        if browsers_path:
            # 设置环境变量，告知Playwright去哪里寻找浏览器二进制文件
            os.environ['PLAYWRIGHT_BROWSERS_PATH'] = str(browsers_path)
            print_info(f"打包环境已检测到。Playwright浏览器路径已动态设置为: {browsers_path}")
        return True
    else:
        # 标准开发环境
        print_debug("标准开发环境，将使用系统默认的Playwright浏览器缓存。")
        return False
