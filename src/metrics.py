# -*- coding: utf-8 -*-
"""
指标收集模块 - 应用性能监控和业务指标
=====================================

本模块提供全面的指标收集、聚合和导出功能。
支持计数器、仪表盘、直方图等多种指标类型，
为应用性能监控和业务分析提供数据支持。

主要功能:
- 多种指标类型支持 (Counter, Gauge, Histogram)
- 标签和维度管理
- 指标聚合和统计
- 导出和持久化
- 实时监控和告警

技术特性:
- 线程安全的指标收集
- 内存高效的数据结构
- 可配置的采样策略
- 多种导出格式支持

作者: nt_dl_team
版本: v2.3.0

⚠️ 监控声明:
本模块收集的所有指标均用于技术监控和性能分析。
- 数据用途: 仅用于应用性能优化和问题诊断
- 隐私保护: 不收集用户敏感信息
- 数据安全: 本地存储，不上传到外部服务
- 使用限制: 仅供技术学习和系统监控
- 责任声明: 监控数据的使用由用户自行承担责任
"""

import time
import threading
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path
import json

from .logging_utils import get_logger

logger = get_logger(__name__)


@dataclass
class MetricPoint:
    """指标数据点"""
    timestamp: float
    value: float
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class MetricSummary:
    """指标摘要统计"""
    count: int = 0
    sum: float = 0.0
    min: float = float('inf')
    max: float = float('-inf')
    avg: float = 0.0
    
    def update(self, value: float):
        """更新统计信息"""
        self.count += 1
        self.sum += value
        self.min = min(self.min, value)
        self.max = max(self.max, value)
        self.avg = self.sum / self.count


class Counter:
    """计数器指标"""
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.value = 0
        self.tags = {}
        self._lock = threading.Lock()
    
    def increment(self, amount: float = 1.0, tags: Dict[str, str] = None):
        """递增计数器"""
        with self._lock:
            self.value += amount
            if tags:
                self.tags.update(tags)
        
        logger.debug(f"Counter {self.name} incremented by {amount}, total: {self.value}")
    
    def reset(self):
        """重置计数器"""
        with self._lock:
            self.value = 0
            self.tags.clear()
        
        logger.debug(f"Counter {self.name} reset")
    
    def get_value(self) -> float:
        """获取当前值"""
        with self._lock:
            return self.value


class Gauge:
    """仪表盘指标"""
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.value = 0.0
        self.tags = {}
        self._lock = threading.Lock()
    
    def set(self, value: float, tags: Dict[str, str] = None):
        """设置仪表盘值"""
        with self._lock:
            self.value = value
            if tags:
                self.tags.update(tags)
        
        logger.debug(f"Gauge {self.name} set to {value}")
    
    def increment(self, amount: float = 1.0):
        """递增仪表盘值"""
        with self._lock:
            self.value += amount
        
        logger.debug(f"Gauge {self.name} incremented by {amount}, current: {self.value}")
    
    def decrement(self, amount: float = 1.0):
        """递减仪表盘值"""
        with self._lock:
            self.value -= amount
        
        logger.debug(f"Gauge {self.name} decremented by {amount}, current: {self.value}")
    
    def get_value(self) -> float:
        """获取当前值"""
        with self._lock:
            return self.value


class Histogram:
    """直方图指标"""
    
    def __init__(self, name: str, description: str = "", buckets: List[float] = None):
        self.name = name
        self.description = description
        self.buckets = buckets or [0.1, 0.5, 1.0, 2.5, 5.0, 10.0, float('inf')]
        self.bucket_counts = {bucket: 0 for bucket in self.buckets}
        self.observations = deque(maxlen=10000)  # 保留最近10000个观测值
        self.summary = MetricSummary()
        self._lock = threading.Lock()
    
    def observe(self, value: float, tags: Dict[str, str] = None):
        """记录观测值"""
        with self._lock:
            # 更新桶计数
            for bucket in self.buckets:
                if value <= bucket:
                    self.bucket_counts[bucket] += 1
            
            # 添加观测值
            point = MetricPoint(
                timestamp=time.time(),
                value=value,
                tags=tags or {}
            )
            self.observations.append(point)
            
            # 更新摘要统计
            self.summary.update(value)
        
        logger.debug(f"Histogram {self.name} observed value {value}")
    
    def get_percentile(self, percentile: float) -> float:
        """获取百分位数"""
        with self._lock:
            if not self.observations:
                return 0.0
            
            values = sorted([obs.value for obs in self.observations])
            index = int(len(values) * percentile / 100)
            return values[min(index, len(values) - 1)]
    
    def get_summary(self) -> MetricSummary:
        """获取摘要统计"""
        with self._lock:
            return self.summary


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        self.counters: Dict[str, Counter] = {}
        self.gauges: Dict[str, Gauge] = {}
        self.histograms: Dict[str, Histogram] = {}
        self._lock = threading.Lock()
        
        # 初始化系统指标
        self._init_system_metrics()
        
        logger.info("指标收集器初始化完成")
    
    def _init_system_metrics(self):
        """初始化系统指标"""
        # 下载相关指标
        self.counter("downloads_total", "总下载次数")
        self.counter("downloads_success", "成功下载次数")
        self.counter("downloads_failed", "失败下载次数")
        
        # 性能指标
        self.histogram("download_duration_seconds", "下载耗时(秒)")
        self.histogram("file_size_bytes", "文件大小(字节)")
        self.histogram("network_speed_mbps", "网络速度(Mbps)")
        
        # 系统资源指标
        self.gauge("memory_usage_mb", "内存使用量(MB)")
        self.gauge("cpu_usage_percent", "CPU使用率(%)")
        self.gauge("disk_usage_mb", "磁盘使用量(MB)")
        
        # 错误指标
        self.counter("errors_total", "总错误次数")
        self.counter("network_errors", "网络错误次数")
        self.counter("validation_errors", "验证错误次数")
    
    def counter(self, name: str, description: str = "") -> Counter:
        """获取或创建计数器"""
        with self._lock:
            if name not in self.counters:
                self.counters[name] = Counter(name, description)
            return self.counters[name]
    
    def gauge(self, name: str, description: str = "") -> Gauge:
        """获取或创建仪表盘"""
        with self._lock:
            if name not in self.gauges:
                self.gauges[name] = Gauge(name, description)
            return self.gauges[name]
    
    def histogram(self, name: str, description: str = "", buckets: List[float] = None) -> Histogram:
        """获取或创建直方图"""
        with self._lock:
            if name not in self.histograms:
                self.histograms[name] = Histogram(name, description, buckets)
            return self.histograms[name]
    
    def increment_counter(self, name: str, amount: float = 1.0, tags: Dict[str, str] = None):
        """递增计数器"""
        counter = self.counter(name)
        counter.increment(amount, tags)
    
    def set_gauge(self, name: str, value: float, tags: Dict[str, str] = None):
        """设置仪表盘值"""
        gauge = self.gauge(name)
        gauge.set(value, tags)
    
    def observe_histogram(self, name: str, value: float, tags: Dict[str, str] = None):
        """记录直方图观测值"""
        histogram = self.histogram(name)
        histogram.observe(value, tags)
    
    def record_download_start(self, url: str):
        """记录下载开始"""
        self.increment_counter("downloads_total", tags={"url": url})
        logger.info(f"记录下载开始: {url}")
    
    def record_download_success(self, url: str, duration: float, file_size: int, speed_mbps: float):
        """记录下载成功"""
        tags = {"url": url, "status": "success"}
        
        self.increment_counter("downloads_success", tags=tags)
        self.observe_histogram("download_duration_seconds", duration, tags)
        self.observe_histogram("file_size_bytes", file_size, tags)
        self.observe_histogram("network_speed_mbps", speed_mbps, tags)
        
        logger.info(f"记录下载成功: {url}, 耗时: {duration:.2f}s, 大小: {file_size/1024/1024:.1f}MB")
    
    def record_download_failure(self, url: str, error_type: str, error_message: str):
        """记录下载失败"""
        tags = {"url": url, "status": "failed", "error_type": error_type}
        
        self.increment_counter("downloads_failed", tags=tags)
        self.increment_counter("errors_total", tags=tags)
        
        if "network" in error_type.lower():
            self.increment_counter("network_errors", tags=tags)
        elif "validation" in error_type.lower():
            self.increment_counter("validation_errors", tags=tags)
        
        logger.warning(f"记录下载失败: {url}, 错误: {error_message}")
    
    def record_system_metrics(self):
        """记录系统指标"""
        try:
            import psutil
            
            # 内存使用
            memory = psutil.virtual_memory()
            self.set_gauge("memory_usage_mb", memory.used / 1024 / 1024)
            
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.set_gauge("cpu_usage_percent", cpu_percent)
            
            # 磁盘使用
            disk = psutil.disk_usage('.')
            self.set_gauge("disk_usage_mb", disk.used / 1024 / 1024)
            
        except ImportError:
            logger.warning("psutil未安装，无法收集系统指标")
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        summary = {
            "counters": {},
            "gauges": {},
            "histograms": {}
        }
        
        with self._lock:
            # 计数器摘要
            for name, counter in self.counters.items():
                summary["counters"][name] = {
                    "value": counter.get_value(),
                    "description": counter.description,
                    "tags": counter.tags
                }
            
            # 仪表盘摘要
            for name, gauge in self.gauges.items():
                summary["gauges"][name] = {
                    "value": gauge.get_value(),
                    "description": gauge.description,
                    "tags": gauge.tags
                }
            
            # 直方图摘要
            for name, histogram in self.histograms.items():
                hist_summary = histogram.get_summary()
                summary["histograms"][name] = {
                    "count": hist_summary.count,
                    "sum": hist_summary.sum,
                    "avg": hist_summary.avg,
                    "min": hist_summary.min,
                    "max": hist_summary.max,
                    "p50": histogram.get_percentile(50),
                    "p95": histogram.get_percentile(95),
                    "p99": histogram.get_percentile(99),
                    "description": histogram.description
                }
        
        return summary
    
    def export_metrics(self, file_path: str = "metrics.json"):
        """导出指标到文件"""
        try:
            summary = self.get_metrics_summary()
            summary["timestamp"] = time.time()
            summary["export_time"] = time.strftime("%Y-%m-%d %H:%M:%S")
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False)
            
            logger.info(f"指标已导出到: {file_path}")
            
        except Exception as e:
            logger.error(f"导出指标失败: {e}")
    
    def reset_all_metrics(self):
        """重置所有指标"""
        with self._lock:
            for counter in self.counters.values():
                counter.reset()
            
            for gauge in self.gauges.values():
                gauge.set(0.0)
            
            for histogram in self.histograms.values():
                histogram.observations.clear()
                histogram.summary = MetricSummary()
                histogram.bucket_counts = {bucket: 0 for bucket in histogram.buckets}
        
        logger.info("所有指标已重置")


class MetricsReporter:
    """指标报告器"""
    
    def __init__(self, collector: MetricsCollector, report_interval: int = 60):
        self.collector = collector
        self.report_interval = report_interval
        self.running = False
        self._thread = None
    
    def start(self):
        """开始定期报告"""
        if self.running:
            return
        
        self.running = True
        self._thread = threading.Thread(target=self._report_loop, daemon=True)
        self._thread.start()
        
        logger.info(f"指标报告器已启动，报告间隔: {self.report_interval}秒")
    
    def stop(self):
        """停止定期报告"""
        self.running = False
        if self._thread:
            self._thread.join(timeout=5)
        
        logger.info("指标报告器已停止")
    
    def _report_loop(self):
        """报告循环"""
        while self.running:
            try:
                # 收集系统指标
                self.collector.record_system_metrics()
                
                # 生成报告
                self.generate_report()
                
                # 等待下一个报告周期
                time.sleep(self.report_interval)
                
            except Exception as e:
                logger.error(f"指标报告失败: {e}")
                time.sleep(5)  # 错误时短暂等待
    
    def generate_report(self):
        """生成指标报告"""
        summary = self.collector.get_metrics_summary()
        
        # 记录关键指标
        counters = summary.get("counters", {})
        if "downloads_total" in counters:
            total = counters["downloads_total"]["value"]
            success = counters.get("downloads_success", {}).get("value", 0)
            failed = counters.get("downloads_failed", {}).get("value", 0)
            
            if total > 0:
                success_rate = (success / total) * 100
                logger.info(f"下载统计 - 总计: {total}, 成功: {success}, 失败: {failed}, 成功率: {success_rate:.1f}%")
        
        # 记录性能指标
        histograms = summary.get("histograms", {})
        if "download_duration_seconds" in histograms:
            duration_stats = histograms["download_duration_seconds"]
            if duration_stats["count"] > 0:
                logger.info(f"下载性能 - 平均耗时: {duration_stats['avg']:.2f}s, P95: {duration_stats['p95']:.2f}s")


# 全局指标收集器实例
metrics_collector = MetricsCollector()
metrics_reporter = MetricsReporter(metrics_collector)