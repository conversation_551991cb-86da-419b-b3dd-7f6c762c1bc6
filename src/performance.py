# -*- coding: utf-8 -*-
"""
性能监控模块
提供下载性能统计和监控功能
"""

import time
from dataclasses import dataclass
from typing import Dict, List, Optional

from .utils import print_info, print_success


@dataclass
class DownloadStats:
    """下载统计信息"""
    url: str
    start_time: float
    end_time: Optional[float] = None
    file_size: Optional[int] = None
    success: bool = False
    error_message: Optional[str] = None
    
    @property
    def duration(self) -> float:
        """获取下载耗时（秒）"""
        if self.end_time:
            return self.end_time - self.start_time
        return time.time() - self.start_time
    
    @property
    def speed_mbps(self) -> float:
        """获取下载速度（MB/s）"""
        if self.file_size and self.end_time:
            duration = self.duration
            if duration > 0:
                return (self.file_size / 1024 / 1024) / duration
        return 0.0


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.stats: List[DownloadStats] = []
        self.session_start_time = time.time()
    
    def start_download(self, url: str) -> DownloadStats:
        """开始下载监控"""
        stat = DownloadStats(url=url, start_time=time.time())
        self.stats.append(stat)
        return stat
    
    def finish_download(self, stat: DownloadStats, success: bool, 
                       file_size: Optional[int] = None, 
                       error_message: Optional[str] = None) -> None:
        """结束下载监控"""
        stat.end_time = time.time()
        stat.success = success
        stat.file_size = file_size
        stat.error_message = error_message
    
    def get_session_stats(self) -> Dict:
        """获取会话统计信息"""
        total_downloads = len(self.stats)
        successful_downloads = sum(1 for s in self.stats if s.success)
        failed_downloads = total_downloads - successful_downloads
        
        total_size = sum(s.file_size or 0 for s in self.stats if s.success)
        total_duration = sum(s.duration for s in self.stats if s.end_time)
        
        avg_speed = 0.0
        if total_duration > 0 and total_size > 0:
            avg_speed = (total_size / 1024 / 1024) / total_duration
        
        session_duration = time.time() - self.session_start_time
        
        return {
            'total_downloads': total_downloads,
            'successful_downloads': successful_downloads,
            'failed_downloads': failed_downloads,
            'success_rate': successful_downloads / total_downloads if total_downloads > 0 else 0,
            'total_size_mb': total_size / 1024 / 1024,
            'total_duration': total_duration,
            'avg_speed_mbps': avg_speed,
            'session_duration': session_duration
        }
    
    def display_stats(self) -> None:
        """显示统计信息"""
        stats = self.get_session_stats()
        
        print_info("\n📊 下载性能统计")
        print_info("=" * 40)
        print_info(f"总下载数: {stats['total_downloads']}")
        print_success(f"成功下载: {stats['successful_downloads']}")
        if stats['failed_downloads'] > 0:
            print_info(f"失败下载: {stats['failed_downloads']}")
        
        print_info(f"成功率: {stats['success_rate']:.1%}")
        
        if stats['total_size_mb'] > 0:
            print_info(f"总下载量: {stats['total_size_mb']:.1f} MB")
        
        if stats['avg_speed_mbps'] > 0:
            print_info(f"平均速度: {stats['avg_speed_mbps']:.1f} MB/s")
        
        print_info(f"会话时长: {stats['session_duration']:.1f} 秒")


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()