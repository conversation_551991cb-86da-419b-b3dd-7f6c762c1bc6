# -*- coding: utf-8 -*-
"""
错误恢复模块
提供智能的错误恢复和重试机制
"""

import asyncio
import time
from dataclasses import dataclass
from typing import Any, Callable, Dict, List, Optional, Tuple
from pathlib import Path

from .utils import print_debug, print_info, print_warn, print_error, ProcessingError
from .logging_utils import get_logger


@dataclass
class RetryConfig:
    """重试配置"""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    backoff_factor: float = 2.0
    jitter: bool = True


@dataclass
class ErrorContext:
    """错误上下文信息"""
    function_name: str
    args: Tuple
    kwargs: Dict
    attempt: int
    last_error: Optional[Exception] = None
    start_time: float = 0.0


class ErrorRecovery:
    """错误恢复管理器"""
    
    def __init__(self):
        self.logger = get_logger()
        self.recovery_strategies: Dict[type, Callable] = {
            ConnectionError: self._handle_connection_error,
            TimeoutError: self._handle_timeout_error,
            FileNotFoundError: self._handle_file_not_found,
            PermissionError: self._handle_permission_error,
            ProcessingError: self._handle_processing_error,
        }
    
    async def retry_with_recovery(
        self, 
        func: Callable, 
        *args, 
        config: Optional[RetryConfig] = None,
        **kwargs
    ) -> Any:
        """带恢复机制的重试执行"""
        if config is None:
            config = RetryConfig()
        
        context = ErrorContext(
            function_name=func.__name__,
            args=args,
            kwargs=kwargs,
            attempt=0,
            start_time=time.time()
        )
        
        for attempt in range(1, config.max_attempts + 1):
            context.attempt = attempt
            
            try:
                self.logger.debug(f"执行 {context.function_name} (尝试 {attempt}/{config.max_attempts})")
                
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                if attempt > 1:
                    self.logger.info(f"✅ {context.function_name} 在第 {attempt} 次尝试后成功")
                
                return result
                
            except Exception as e:
                context.last_error = e
                self.logger.warning(f"❌ {context.function_name} 第 {attempt} 次尝试失败: {e}")
                
                # 如果是最后一次尝试，直接抛出异常
                if attempt == config.max_attempts:
                    self.logger.error(f"💥 {context.function_name} 所有重试均失败")
                    raise e
                
                # 尝试错误恢复
                recovery_success = await self._attempt_recovery(e, context)
                if not recovery_success:
                    self.logger.warning(f"⚠️ 错误恢复失败，继续重试")
                
                # 计算延迟时间
                delay = self._calculate_delay(attempt, config)
                self.logger.debug(f"等待 {delay:.1f} 秒后重试...")
                await asyncio.sleep(delay)
    
    async def _attempt_recovery(self, error: Exception, context: ErrorContext) -> bool:
        """尝试错误恢复"""
        error_type = type(error)
        
        # 查找匹配的恢复策略
        for registered_type, strategy in self.recovery_strategies.items():
            if issubclass(error_type, registered_type):
                try:
                    self.logger.debug(f"尝试恢复策略: {strategy.__name__}")
                    success = await strategy(error, context)
                    if success:
                        self.logger.info(f"✅ 错误恢复成功: {strategy.__name__}")
                        return True
                except Exception as recovery_error:
                    self.logger.warning(f"恢复策略失败: {recovery_error}")
        
        return False
    
    def _calculate_delay(self, attempt: int, config: RetryConfig) -> float:
        """计算重试延迟时间"""
        delay = config.base_delay * (config.backoff_factor ** (attempt - 1))
        delay = min(delay, config.max_delay)
        
        if config.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)  # 添加50%的随机抖动
        
        return delay
    
    # 恢复策略实现
    async def _handle_connection_error(self, error: Exception, context: ErrorContext) -> bool:
        """处理连接错误"""
        self.logger.info("🔄 检测到连接错误，尝试网络恢复...")
        
        # 等待网络恢复
        await asyncio.sleep(2.0)
        
        # 可以添加网络连通性检查
        return True
    
    async def _handle_timeout_error(self, error: Exception, context: ErrorContext) -> bool:
        """处理超时错误"""
        self.logger.info("⏰ 检测到超时错误，调整超时参数...")
        
        # 动态调整超时时间
        if 'timeout' in context.kwargs:
            old_timeout = context.kwargs['timeout']
            new_timeout = min(old_timeout * 1.5, 600)  # 最大10分钟
            context.kwargs['timeout'] = new_timeout
            self.logger.info(f"超时时间调整: {old_timeout}s -> {new_timeout}s")
        
        return True
    
    async def _handle_file_not_found(self, error: Exception, context: ErrorContext) -> bool:
        """处理文件未找到错误"""
        self.logger.info("📁 检测到文件未找到错误，尝试创建目录...")
        
        # 尝试创建父目录
        if len(context.args) > 0:
            file_path = Path(str(context.args[0]))
            if file_path.parent:
                try:
                    file_path.parent.mkdir(parents=True, exist_ok=True)
                    self.logger.info(f"✅ 创建目录: {file_path.parent}")
                    return True
                except Exception as e:
                    self.logger.warning(f"创建目录失败: {e}")
        
        return False
    
    async def _handle_permission_error(self, error: Exception, context: ErrorContext) -> bool:
        """处理权限错误"""
        self.logger.info("🔐 检测到权限错误，尝试权限修复...")
        
        # 在实际应用中，这里可以尝试修改文件权限或提示用户
        self.logger.warning("权限错误需要手动处理")
        return False
    
    async def _handle_processing_error(self, error: Exception, context: ErrorContext) -> bool:
        """处理处理错误"""
        self.logger.info("⚙️ 检测到处理错误，尝试清理和重置...")
        
        # 可以添加特定的清理逻辑
        await asyncio.sleep(1.0)
        return True


# 全局错误恢复实例
error_recovery = ErrorRecovery()


# 装饰器函数
def with_retry(config: Optional[RetryConfig] = None):
    """重试装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            return await error_recovery.retry_with_recovery(func, *args, config=config, **kwargs)
        return wrapper
    return decorator