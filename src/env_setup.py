# -*- coding: utf-8 -*-
"""
环境设置模块
负责目录管理、环境初始化和清理
"""

import argparse
import os
import shutil
import signal
import sys
import time
from pathlib import Path
from typing import Optional

from .utils import print_debug, print_info, print_success, print_warn


class EnvironmentManager:
    """环境管理器"""
    
    def __init__(self):
        self.music_dir: Optional[Path] = None
        self.temp_dir: Optional[Path] = None
        self.current_browser_data_dir: Optional[Path] = None
        self._signal_handlers_set = False
    
    def initialize_environment(self) -> None:
        """初始化环境"""
        # 设置信号处理器
        self.setup_signal_handlers()
        
        # 获取下载目录
        self.music_dir = self.get_download_directory()
        self.temp_dir = self.music_dir / ".temp_downloads"
        
        # 生成唯一的浏览器数据目录
        self.current_browser_data_dir = self.generate_unique_browser_data_dir()
        
        # 启动时清理遗留的浏览器数据目录
        self.cleanup_legacy_browser_data_dirs()
        
        # 确保目录存在
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self.current_browser_data_dir.mkdir(parents=True, exist_ok=True)
        
        print_debug("环境初始化完成")
        print_debug(f"音乐目录: {self.music_dir}")
        print_debug(f"临时目录: {self.temp_dir}")
        print_debug(f"浏览器数据目录: {self.current_browser_data_dir}")
    
    def setup_signal_handlers(self) -> None:
        """设置信号处理器"""
        if self._signal_handlers_set:
            return
            
        def signal_handler(signum, frame):
            print_warn("\n⚠️  检测到中断信号，正在安全退出...")
            self.cleanup_current_browser_data()
            sys.exit(0)
        
        try:
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            self._signal_handlers_set = True
            print_debug("信号处理器设置完成")
        except Exception as e:
            print_warn(f"设置信号处理器失败: {e}")
    
    def get_download_directory(self) -> Path:
        """获取用户指定的下载目录，如果未指定则使用默认Downloads文件夹"""
        parser = argparse.ArgumentParser(description='网易云音乐下载器')
        parser.add_argument(
            'download_path',
            nargs='?',
            default=None,
            help='指定音乐下载保存的文件夹路径（可选）'
        )
        
        args = parser.parse_args()
        
        if args.download_path:
            # 用户指定了路径
            specified_path = Path(args.download_path).expanduser().resolve()
            if not specified_path.exists():
                try:
                    specified_path.mkdir(parents=True, exist_ok=True)
                    print_success(f"✓ 已创建指定目录: {specified_path}")
                except Exception as e:
                    print_warn(f"错误: 无法创建指定目录 {specified_path}: {e}")
                    print_warn("将使用默认下载目录")
                    return self.get_default_downloads_path()
            else:
                print_info(f"✓ 使用指定的下载目录: {specified_path}")
            return specified_path
        else:
            # 用户未指定，使用默认路径
            return self.get_default_downloads_path()
    
    def get_default_downloads_path(self) -> Path:
        """获取默认Downloads路径下的nt_dl_downloads文件夹"""
        try:
            downloads_path = Path.home() / "Downloads" / "nt_dl_downloads"
            downloads_path.mkdir(parents=True, exist_ok=True)
            print_info(f"✓ 使用默认下载目录: {downloads_path}")
            print_info("💡 提示: 下次可通过命令行参数指定自定义路径")
            print_info("   例如: python main.py /path/to/your/folder")
            return downloads_path
        except Exception as e:
            # 如果无法访问Downloads，则使用当前目录
            fallback_path = Path.cwd() / "nt_dl_downloads"
            fallback_path.mkdir(exist_ok=True)
            print_warn(f"警告: 无法访问Downloads目录，使用当前目录: {fallback_path}")
            return fallback_path
    
    def generate_unique_browser_data_dir(self) -> Path:
        """在下载目录中生成唯一的浏览器数据目录名"""
        if self.music_dir is None:
            # 如果music_dir还未设置，使用临时路径
            temp_base = Path.cwd() / "temp_browser_data"
        else:
            temp_base = self.music_dir
            
        process_id = os.getpid()
        timestamp = int(time.time())
        
        # 格式：.browser_data_进程ID_时间戳（隐藏目录）
        dir_name = f".browser_data_{process_id}_{timestamp}"
        return temp_base / dir_name
    
    def cleanup_legacy_browser_data_dirs(self) -> None:
        """清理下载目录中遗留的浏览器数据目录"""
        print_info("🧹 检查下载目录中遗留的browser_data目录...")
        
        cleaned_count = 0
        total_size_freed = 0
        
        try:
            for item in self.music_dir.iterdir():
                if item.is_dir() and self.is_browser_data_directory(item.name):
                    if not self.is_directory_in_use(item):
                        # 计算目录大小
                        try:
                            size = self.calculate_directory_size(item)
                            total_size_freed += size
                        except:
                            pass
                        
                        # 删除目录
                        try:
                            shutil.rmtree(item)
                            print_debug(f"✓ 已清理遗留目录: {item.name}")
                            cleaned_count += 1
                        except Exception as e:
                            print_warn(f"⚠️  无法清理目录 {item.name}: {e}")
                    else:
                        print_debug(f"跳过正在使用的目录: {item.name}")
        except Exception as e:
            print_warn(f"扫描遗留目录时出错: {e}")
        
        if cleaned_count > 0:
            print_success(
                f"✅ 已清理 {cleaned_count} 个遗留的browser_data目录，"
                f"释放空间: {total_size_freed / 1024 / 1024:.2f} MB"
            )
        else:
            print_info("✓ 下载目录中未发现需要清理的遗留目录")
    
    def is_browser_data_directory(self, dir_name: str) -> bool:
        """检查目录名是否是browser_data相关目录"""
        return (
            dir_name == ".browser_data" or
            dir_name.startswith(".browser_data_") or
            dir_name == "browser_data" or
            dir_name.startswith("browser_data_") or
            (dir_name.startswith("browser_data") and 
             ("_" in dir_name or dir_name[-1].isdigit()))
        )
    
    def is_directory_in_use(self, dir_path: Path) -> bool:
        """检查目录是否正在被使用"""
        # 检查是否存在Chrome进程锁文件
        lock_files = ["SingletonLock", "lockfile", ".lock", "Singleton"]
        
        for lock_file in lock_files:
            if (dir_path / lock_file).exists():
                return True
        
        # 检查目录是否最近被修改（5分钟内）
        try:
            stat = dir_path.stat()
            if time.time() - stat.st_mtime < 300:  # 5分钟
                return True
        except:
            pass
        
        return False
    
    def calculate_directory_size(self, dir_path: Path) -> int:
        """计算目录大小"""
        total_size = 0
        try:
            for item in dir_path.rglob("*"):
                if item.is_file():
                    total_size += item.stat().st_size
        except:
            pass
        return total_size
    
    def cleanup_current_browser_data(self) -> None:
        """清理当前的浏览器数据目录"""
        if self.current_browser_data_dir and self.current_browser_data_dir.exists():
            try:
                shutil.rmtree(self.current_browser_data_dir)
                print_debug(f"✓ 当前browser_data目录已清理: {self.current_browser_data_dir}")
            except Exception as e:
                print_warn(f"⚠️  清理当前browser_data目录失败: {e}")
    
    def cleanup_and_exit(self) -> None:
        """清理并退出"""
        from .file_handler import cleanup_temp_directory
        cleanup_temp_directory(self.temp_dir, self.music_dir)
        self.cleanup_current_browser_data()
        print_info("程序已安全退出")


# 全局环境管理器实例
env_manager = EnvironmentManager()