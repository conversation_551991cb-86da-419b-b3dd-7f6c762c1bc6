#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller spec文件生成器
用于创建适合的.spec文件来打包应用程序
"""

import sys
from pathlib import Path

def create_spec_file():
    """创建PyInstaller spec文件"""
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 分析主程序
a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        # 包含src目录下的所有Python文件
        ('src', 'src'),
        # 包含配置和文档文件
        ('requirements.txt', '.'),
        ('README.md', '.'),
    ],
    hiddenimports=[
        # Playwright相关的隐藏导入
        'playwright',
        'playwright.async_api',
        'playwright._impl',
        'playwright._impl._browser_type',
        'playwright._impl._connection',
        'playwright._impl._driver',
        'playwright._impl._playwright',
        # 其他依赖
        'requests',
        'bs4',
        'Crypto',
        'Crypto.Cipher',
        'Crypto.Util.Padding',
        # 项目模块
        'src.api',
        'src.config',
        'src.crypto',
        'src.downloader',
        'src.env_setup',
        'src.file_handler',
        'src.logging_utils',
        'src.performance',
        'src.pyinstaller_compat',
        'src.ui',
        'src.utils',
        'src.config_validator',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的模块以减小体积
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 创建PYZ文件
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 创建可执行文件
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='netease_downloader',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

# 创建COLLECT（文件夹分发）
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='netease_downloader'
)
'''
    
    # 写入spec文件
    spec_file = Path("netease_downloader.spec")
    with open(spec_file, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print(f"✅ 已创建PyInstaller spec文件: {spec_file}")
    print("\n📋 打包步骤:")
    print("1. 安装PyInstaller: pip install pyinstaller")
    print("2. 运行打包命令: pyinstaller netease_downloader.spec")
    print("3. 手动复制ms-playwright文件夹到dist/netease_downloader/目录下")
    print("4. 运行可执行文件: ./dist/netease_downloader/netease_downloader")
    
    return spec_file

def create_build_script():
    """创建自动化构建脚本"""
    
    build_script_content = '''#!/bin/bash
# 网易云音乐下载器自动化构建脚本

set -e  # 遇到错误立即退出

echo "🚀 开始构建网易云音乐下载器..."

# 检查Python环境
echo "📋 检查Python环境..."
python --version
pip --version

# 安装依赖
echo "📦 安装依赖包..."
pip install -r requirements.txt
pip install pyinstaller

# 运行测试
echo "🧪 运行测试..."
python test_modules.py

# 创建spec文件
echo "📝 创建spec文件..."
python build_spec.py

# 执行打包
echo "📦 开始打包..."
pyinstaller netease_downloader.spec

# 检查打包结果
if [ -d "dist/netease_downloader" ]; then
    echo "✅ 打包成功！"
    echo "📁 输出目录: dist/netease_downloader/"
    echo ""
    echo "⚠️  重要提醒:"
    echo "请手动将ms-playwright文件夹复制到 dist/netease_downloader/ 目录下"
    echo "文件夹结构应为:"
    echo "  dist/netease_downloader/"
    echo "  ├── netease_downloader (可执行文件)"
    echo "  ├── ms-playwright/"
    echo "  │   └── chromium-1179/"
    echo "  │       └── ..."
    echo "  └── ..."
    echo ""
    echo "🎉 构建完成！"
else
    echo "❌ 打包失败！"
    exit 1
fi
'''
    
    # 写入构建脚本
    build_script = Path("build.sh")
    with open(build_script, 'w', encoding='utf-8') as f:
        f.write(build_script_content)
    
    # 设置执行权限
    import stat
    build_script.chmod(build_script.stat().st_mode | stat.S_IEXEC)
    
    print(f"✅ 已创建构建脚本: {build_script}")
    print("💡 使用方法: ./build.sh")
    
    return build_script

def create_windows_build_script():
    """创建Windows构建脚本"""
    
    build_script_content = '''@echo off
REM 网易云音乐下载器Windows自动化构建脚本

echo 🚀 开始构建网易云音乐下载器...

REM 检查Python环境
echo 📋 检查Python环境...
python --version
pip --version

REM 安装依赖
echo 📦 安装依赖包...
pip install -r requirements.txt
pip install pyinstaller

REM 运行测试
echo 🧪 运行测试...
python test_modules.py

REM 创建spec文件
echo 📝 创建spec文件...
python build_spec.py

REM 执行打包
echo 📦 开始打包...
pyinstaller netease_downloader.spec

REM 检查打包结果
if exist "dist\\netease_downloader" (
    echo ✅ 打包成功！
    echo 📁 输出目录: dist\\netease_downloader\\
    echo.
    echo ⚠️  重要提醒:
    echo 请手动将ms-playwright文件夹复制到 dist\\netease_downloader\\ 目录下
    echo 文件夹结构应为:
    echo   dist\\netease_downloader\\
    echo   ├── netease_downloader.exe
    echo   ├── ms-playwright\\
    echo   │   └── chromium-1179\\
    echo   │       └── ...
    echo   └── ...
    echo.
    echo 🎉 构建完成！
) else (
    echo ❌ 打包失败！
    exit /b 1
)

pause
'''
    
    # 写入Windows构建脚本
    build_script = Path("build.bat")
    with open(build_script, 'w', encoding='utf-8') as f:
        f.write(build_script_content)
    
    print(f"✅ 已创建Windows构建脚本: {build_script}")
    print("💡 使用方法: build.bat")
    
    return build_script

def main():
    """主函数"""
    print("🛠️ PyInstaller构建工具")
    print("=" * 50)
    
    # 创建spec文件
    spec_file = create_spec_file()
    
    # 创建构建脚本
    build_script = create_build_script()
    windows_script = create_windows_build_script()
    
    print("\n" + "=" * 50)
    print("📋 构建文件已创建:")
    print(f"  - {spec_file} (PyInstaller配置)")
    print(f"  - {build_script} (Linux/macOS构建脚本)")
    print(f"  - {windows_script} (Windows构建脚本)")
    
    print("\n💡 下一步:")
    print("1. 根据你的操作系统运行相应的构建脚本")
    print("2. 等待打包完成")
    print("3. 手动复制ms-playwright文件夹到输出目录")
    print("4. 测试可执行文件")

if __name__ == "__main__":
    main()