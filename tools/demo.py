#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网易云音乐下载器 - 演示脚本
展示模块化版本的功能和架构
"""

import sys
from pathlib import Path

def demo_architecture():
    """演示架构特性"""
    print("🏗️ 模块化架构演示")
    print("=" * 60)
    
    # 展示模块结构
    print("📦 模块结构:")
    modules = [
        ("config.py", "全局配置和常量"),
        ("utils.py", "工具函数和错误处理"),
        ("crypto.py", "网易云API加密算法"),
        ("api.py", "URL解析和数据获取"),
        ("env_setup.py", "环境管理和清理"),
        ("file_handler.py", "文件处理和音频合成"),
        ("ui.py", "用户界面和进度显示"),
        ("downloader.py", "浏览器自动化下载")
    ]
    
    for module, description in modules:
        print(f"   📄 {module:<20} - {description}")
    
    print("\n🔧 核心功能:")
    features = [
        "✅ 完整的weapi加密算法实现",
        "✅ 智能的环境管理和清理机制",
        "✅ 轻量级的进度监控系统",
        "✅ 完善的错误处理和重试机制",
        "✅ 自动化的临时文件恢复功能",
        "✅ 隐藏的浏览器数据目录管理",
        "✅ 模块化的代码结构设计"
    ]
    
    for feature in features:
        print(f"   {feature}")

def demo_crypto():
    """演示加密功能"""
    print("\n🔐 加密功能演示")
    print("=" * 60)
    
    from src.crypto import generate_random_key, get_weapi_params
    
    # 演示随机密钥生成
    print("🔑 随机密钥生成:")
    for i in range(3):
        key = generate_random_key(16)
        print(f"   密钥 {i+1}: {key}")
    
    # 演示weapi加密
    print("\n🔒 weapi加密演示:")
    test_payload = {
        "id": "123456",
        "n": 100000,
        "s": 8,
        "csrf_token": ""
    }
    
    encrypted = get_weapi_params(test_payload)
    print(f"   原始数据: {test_payload}")
    print(f"   params长度: {len(encrypted['params'])}")
    print(f"   encSecKey长度: {len(encrypted['encSecKey'])}")
    print(f"   params前50字符: {encrypted['params'][:50]}...")
    print(f"   encSecKey前50字符: {encrypted['encSecKey'][:50]}...")

def demo_environment():
    """演示环境管理"""
    print("\n🏗️ 环境管理演示")
    print("=" * 60)
    
    from src.env_setup import env_manager
    
    # 演示目录生成
    print("📁 目录管理:")
    browser_dir = env_manager.generate_unique_browser_data_dir()
    print(f"   浏览器数据目录: {browser_dir}")
    
    # 演示目录识别
    print("\n🔍 目录识别测试:")
    test_dirs = [
        ".browser_data_12345_67890",
        "browser_data_old",
        "normal_folder",
        ".browser_data",
        "music_files"
    ]
    
    for dir_name in test_dirs:
        is_browser = env_manager.is_browser_data_directory(dir_name)
        status = "🟢 浏览器数据目录" if is_browser else "🔵 普通目录"
        print(f"   {dir_name:<25} -> {status}")

def demo_url_parsing():
    """演示URL解析"""
    print("\n🔗 URL解析演示")
    print("=" * 60)
    
    from src.api import parse_and_route_url
    from src.utils import ProcessingError
    
    # 测试URL格式
    test_urls = [
        "https://music.163.com/song?id=123456",
        "https://music.163.com/playlist?id=789012",
        "https://music.163.com/album?id=345678",
        "https://invalid-url.com/test"
    ]
    
    print("📋 URL格式识别:")
    for url in test_urls:
        try:
            # 这里只测试URL格式识别，不实际发送请求
            result = parse_and_route_url(url)
            print(f"   ✅ {url} -> 识别成功")
        except ProcessingError as e:
            if "URL格式不匹配" in str(e):
                print(f"   ❌ {url} -> 格式不匹配")
            else:
                print(f"   🔄 {url} -> 格式正确（网络请求失败）")
        except Exception as e:
            print(f"   🔄 {url} -> 格式正确（网络请求失败）")

def demo_file_operations():
    """演示文件操作"""
    print("\n📁 文件操作演示")
    print("=" * 60)
    
    from src.file_handler import find_audio_files_recursively
    from src.config import AUDIO_EXTENSIONS
    
    # 展示支持的音频格式
    print("🎵 支持的音频格式:")
    extensions = sorted(list(AUDIO_EXTENSIONS))
    for i, ext in enumerate(extensions):
        if i % 5 == 0:
            print("   ", end="")
        print(f"{ext:<8}", end="")
        if (i + 1) % 5 == 0:
            print()
    if len(extensions) % 5 != 0:
        print()
    
    # 搜索当前目录的音频文件
    print("\n🔍 当前目录音频文件搜索:")
    current_dir = Path(".")
    audio_files = find_audio_files_recursively(current_dir)
    
    if audio_files:
        print(f"   找到 {len(audio_files)} 个音频文件:")
        for audio_file in audio_files[:5]:  # 只显示前5个
            print(f"   📄 {audio_file}")
        if len(audio_files) > 5:
            print(f"   ... 还有 {len(audio_files) - 5} 个文件")
    else:
        print("   当前目录未找到音频文件")

def demo_comparison():
    """对比新旧版本"""
    print("\n📊 新旧版本对比")
    print("=" * 60)
    
    comparison = [
        ("架构设计", "单文件混合", "模块化分离"),
        ("代码维护", "难以维护", "易于维护"),
        ("功能扩展", "修改困难", "轻松扩展"),
        ("错误处理", "基础处理", "完善处理"),
        ("环境管理", "简单清理", "智能管理"),
        ("进度监控", "基础显示", "轻量级优化"),
        ("文件恢复", "手动处理", "自动恢复"),
        ("代码复用", "重复代码", "模块复用"),
    ]
    
    print(f"{'特性':<12} {'旧版本(单文件)':<20} {'新版本(模块化)':<20}")
    print("-" * 60)
    
    for feature, old, new in comparison:
        print(f"{feature:<12} {old:<20} {new:<20}")

def main():
    """主演示函数"""
    print("🎵 网易云音乐下载器 - 模块化版本演示")
    print("基于Rust版本的完整功能迁移")
    print("=" * 60)
    
    demos = [
        ("架构特性", demo_architecture),
        ("加密功能", demo_crypto),
        ("环境管理", demo_environment),
        ("URL解析", demo_url_parsing),
        ("文件操作", demo_file_operations),
        ("版本对比", demo_comparison),
    ]
    
    for demo_name, demo_func in demos:
        try:
            demo_func()
        except Exception as e:
            print(f"\n❌ {demo_name} 演示失败: {e}")
    
    print("\n🎉 演示完成！")
    print("\n💡 使用说明:")
    print("   1. 安装依赖: pip install -r requirements.txt")
    print("   2. 安装浏览器: playwright install chromium")
    print("   3. 安装FFmpeg: brew install ffmpeg (macOS)")
    print("   4. 运行程序: python main.py")
    
    print("\n📚 更多信息请查看 README.md")

if __name__ == "__main__":
    main()